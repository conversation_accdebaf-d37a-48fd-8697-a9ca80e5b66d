'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { FilterableStrainCardsProps, Strain } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';
import Tilt3DCardEffect from '../../visual/Tilt3DCardEffect';

/**
 * FilterableStrainCards Component
 * 
 * Build filterable strain cards with search, category filters, and sorting options.
 * Features comprehensive filtering system for cannabis strains with responsive design.
 * 
 * @example
 * ```tsx
 * <FilterableStrainCards
 *   strains={strains}
 *   enableSearch={true}
 *   filters={[
 *     { id: 'type', name: 'Type', options: ['sativa', 'indica', 'hybrid'] },
 *     { id: 'effects', name: 'Effects', options: ['relaxed', 'energetic', 'creative'] }
 *   ]}
 *   sortOptions={[
 *     { id: 'name', name: 'Name', field: 'name' },
 *     { id: 'thc', name: 'THC %', field: 'thc' }
 *   ]}
 *   cardsPerRow={3}
 *   onStrainClick={(strain) => router.push(`/strains/${strain.id}`)}
 * />
 * ```
 */
const FilterableStrainCards: React.FC<FilterableStrainCardsProps> = ({
  strains,
  enableSearch = true,
  filters = [],
  sortOptions = [],
  cardsPerRow = 3,
  onStrainClick,
  className = '',
  style,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<Record<string, string>>({});
  const [sortBy, setSortBy] = useState<string>('');

  // Filter and sort strains
  const filteredAndSortedStrains = useMemo(() => {
    let result = strains;

    // Apply search filter
    if (searchTerm) {
      result = result.filter(strain =>
        strain.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        strain.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        strain.effects.some(effect => effect.toLowerCase().includes(searchTerm.toLowerCase())) ||
        strain.flavors.some(flavor => flavor.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply category filters
    Object.entries(activeFilters).forEach(([filterId, filterValue]) => {
      if (filterValue) {
        result = result.filter(strain => {
          switch (filterId) {
            case 'type':
              return strain.type === filterValue;
            case 'effects':
              return strain.effects.includes(filterValue);
            case 'flavors':
              return strain.flavors.includes(filterValue);
            case 'thc':
              if (filterValue === 'low') return strain.thc < 15;
              if (filterValue === 'medium') return strain.thc >= 15 && strain.thc <= 25;
              if (filterValue === 'high') return strain.thc > 25;
              return true;
            case 'cbd':
              if (filterValue === 'low') return strain.cbd < 5;
              if (filterValue === 'medium') return strain.cbd >= 5 && strain.cbd <= 15;
              if (filterValue === 'high') return strain.cbd > 15;
              return true;
            default:
              return true;
          }
        });
      }
    });

    // Apply sorting
    if (sortBy) {
      const sortOption = sortOptions.find(option => option.id === sortBy);
      if (sortOption) {
        result = [...result].sort((a, b) => {
          const aValue = a[sortOption.field as keyof Strain];
          const bValue = b[sortOption.field as keyof Strain];
          
          if (typeof aValue === 'string' && typeof bValue === 'string') {
            return aValue.localeCompare(bValue);
          }
          if (typeof aValue === 'number' && typeof bValue === 'number') {
            return bValue - aValue; // Descending order for numbers
          }
          return 0;
        });
      }
    }

    return result;
  }, [strains, searchTerm, activeFilters, sortBy, sortOptions]);

  // Handle filter changes
  const handleFilterChange = (filterId: string, value: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterId]: prev[filterId] === value ? '' : value
    }));
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setActiveFilters({});
    setSortBy('');
  };

  // Get strain type color
  const getStrainTypeColor = (type: string) => {
    switch (type) {
      case 'sativa':
        return brandColors.primary[500];
      case 'indica':
        return brandColors.sage[500];
      case 'hybrid':
        return brandColors.gold[500];
      default:
        return brandColors.charcoal[500];
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: brandAnimations.easing.smooth
      }
    }
  };

  return (
    <div className={`filterable-strain-cards ${className}`} style={style}>
      {/* Search and Filter Controls */}
      <div className="mb-8 space-y-4">
        {/* Search Bar */}
        {enableSearch && (
          <div className="relative max-w-md">
            <input
              type="text"
              placeholder="Search strains..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <svg className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        )}

        {/* Filters and Sort */}
        <div className="flex flex-wrap gap-4 items-center justify-between">
          <div className="flex flex-wrap gap-3">
            {/* Filter Buttons */}
            {filters.map((filter) => (
              <div key={filter.id} className="flex flex-wrap gap-2">
                {filter.options.map((option) => (
                  <button
                    key={option}
                    onClick={() => handleFilterChange(filter.id, option)}
                    className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 capitalize ${
                      activeFilters[filter.id] === option
                        ? 'bg-primary-600 text-cream-50 border-primary-600'
                        : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            ))}
          </div>

          {/* Sort Dropdown */}
          {sortOptions.length > 0 && (
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">Sort by...</option>
              {sortOptions.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.name}
                </option>
              ))}
            </select>
          )}
        </div>

        {/* Active Filters and Clear */}
        {(searchTerm || Object.values(activeFilters).some(v => v) || sortBy) && (
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-2">
              {searchTerm && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700">
                  Search: "{searchTerm}"
                </span>
              )}
              {Object.entries(activeFilters).map(([filterId, value]) => 
                value && (
                  <span key={filterId} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700 capitalize">
                    {filterId}: {value}
                  </span>
                )
              )}
              {sortBy && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700">
                  Sorted by: {sortOptions.find(o => o.id === sortBy)?.name}
                </span>
              )}
            </div>
            <button
              onClick={clearAllFilters}
              className="text-sm text-primary-600 hover:text-primary-700 font-medium"
            >
              Clear All
            </button>
          </div>
        )}

        {/* Results Count */}
        <p className="text-sm text-gray-600">
          Showing {filteredAndSortedStrains.length} of {strains.length} strains
        </p>
      </div>

      {/* Strain Cards Grid */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`${searchTerm}-${JSON.stringify(activeFilters)}-${sortBy}`}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          className={`grid gap-6 ${
            cardsPerRow === 1 ? 'grid-cols-1' :
            cardsPerRow === 2 ? 'grid-cols-1 md:grid-cols-2' :
            cardsPerRow === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
            cardsPerRow === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' :
            'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'
          }`}
        >
          {filteredAndSortedStrains.map((strain) => (
            <motion.div
              key={strain.id}
              variants={cardVariants}
              className="cursor-pointer"
              onClick={() => onStrainClick?.(strain)}
            >
              <Tilt3DCardEffect
                maxTilt={12}
                perspective={1000}
                scale={1.02}
                addDynamicShadow={true}
                speed={300}
              >
                <div
                  className="bg-cream-50 rounded-xl overflow-hidden group transition-all duration-200"
                  style={{ boxShadow: brandShadows.soft }}
                >
                {/* Strain Image */}
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-sage-100">
                  {strain.image ? (
                    <Image
                      src={strain.image}
                      alt={strain.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center">
                        <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 2a6 6 0 00-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 00.515 1.07 32.91 32.91 0 003.256.508 3.5 3.5 0 006.972 0 32.91 32.91 0 003.256-.508.75.75 0 00.515-1.07A11.717 11.717 0 0116 8a6 6 0 00-6-6z" />
                        </svg>
                      </div>
                    </div>
                  )}

                  {/* Strain Type Badge */}
                  <div className="absolute top-3 right-3">
                    <span 
                      className="text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize"
                      style={{ backgroundColor: getStrainTypeColor(strain.type) }}
                    >
                      {strain.type}
                    </span>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300" />
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
                    {strain.name}
                  </h3>

                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {strain.description}
                  </p>

                  {/* THC/CBD Info */}
                  <div className="flex gap-2 mb-4">
                    <span className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
                      THC: {strain.thc}%
                    </span>
                    <span className="text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded">
                      CBD: {strain.cbd}%
                    </span>
                  </div>

                  {/* Effects */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {strain.effects.slice(0, 3).map((effect, idx) => (
                      <span 
                        key={idx}
                        className="text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded"
                      >
                        {effect}
                      </span>
                    ))}
                  </div>

                  {/* Flavors */}
                  <div className="flex flex-wrap gap-1">
                    {strain.flavors.slice(0, 2).map((flavor, idx) => (
                      <span 
                        key={idx}
                        className="text-xs bg-cream-200 text-charcoal-600 px-2 py-1 rounded"
                      >
                        {flavor}
                      </span>
                    ))}
                  </div>

                  {/* Genetics */}
                  {strain.genetics && (
                    <div className="mt-3 pt-3 border-t border-cream-200">
                      <p className="text-xs text-gray-500">
                        <span className="font-medium">Genetics:</span> {strain.genetics}
                      </p>
                    </div>
                  )}
                </div>
              </div>
              </Tilt3DCardEffect>
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>

      {/* Empty State */}
      {filteredAndSortedStrains.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No strains found</h3>
          <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
};

export default FilterableStrainCards;
