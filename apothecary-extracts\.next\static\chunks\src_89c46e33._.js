(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/styles/brand.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Apothecary Farms Brand Utility System
 * Centralized brand tokens for colors, gradients, shadows, and animations
 */ // Brand Colors
__turbopack_context__.s({
    "brandAnimations": (()=>brandAnimations),
    "brandColors": (()=>brandColors),
    "brandGradients": (()=>brandGradients),
    "brandShadows": (()=>brandShadows),
    "brandSpacing": (()=>brandSpacing),
    "brandTypography": (()=>brandTypography),
    "brandUtils": (()=>brandUtils),
    "default": (()=>__TURBOPACK__default__export__),
    "terpeneColors": (()=>terpeneColors)
});
const brandColors = {
    // Primary Green Palette
    primary: {
        50: '#f0f9f4',
        100: '#dcf2e4',
        200: '#bce5cd',
        300: '#8dd1a8',
        400: '#57b67c',
        500: '#359a5a',
        600: '#267d47',
        700: '#1f633a',
        800: '#1b4332',
        900: '#163a2b'
    },
    // Apothecary Green (from memory - the mint green mentioned)
    apothecary: '#2FB886',
    // Cream/Neutral Palette
    cream: {
        50: '#fefefe',
        100: '#f8f6f0',
        200: '#f4f1e8',
        300: '#ede8db',
        400: '#e4dcc8',
        500: '#d8cdb0'
    },
    // Gold/Amber Palette (for extracts/rosin)
    gold: {
        50: '#fefcf7',
        100: '#fdf8ed',
        200: '#f9eed5',
        300: '#f4e4bc',
        400: '#edd5a3',
        500: '#d4a574'
    },
    // Charcoal/Gray Palette
    charcoal: {
        50: '#f8f9fa',
        100: '#e9ecef',
        200: '#dee2e6',
        300: '#ced4da',
        400: '#adb5bd',
        500: '#6c757d',
        600: '#495057',
        700: '#343a40',
        800: '#2d3436',
        900: '#212529'
    },
    // Sage Green Palette
    sage: {
        50: '#f7f9f8',
        100: '#eef2f0',
        200: '#dde5e1',
        300: '#c4d2ca',
        400: '#a5b8ad',
        500: '#95a99c'
    }
};
const terpeneColors = {
    // Pineapple Express - Orange to Yellow
    pineapple: {
        from: '#ff8c00',
        to: '#ffd700',
        gradient: 'linear-gradient(135deg, #ff8c00 0%, #ffd700 100%)'
    },
    // GMO - Blue Frost
    gmo: {
        from: '#4a90e2',
        to: '#87ceeb',
        gradient: 'linear-gradient(135deg, #4a90e2 0%, #87ceeb 100%)'
    },
    // Rosin - Gold to Amber
    rosin: {
        from: '#d4a574',
        to: '#ff8c00',
        gradient: 'linear-gradient(135deg, #d4a574 0%, #ff8c00 100%)'
    },
    // Flower - Green to Yellow
    flower: {
        from: '#359a5a',
        to: '#ffd700',
        gradient: 'linear-gradient(135deg, #359a5a 0%, #ffd700 100%)'
    },
    // Extract - Amber to Purple
    extract: {
        from: '#ff8c00',
        to: '#9370db',
        gradient: 'linear-gradient(135deg, #ff8c00 0%, #9370db 100%)'
    },
    // Particle Palette - Soft colors for backgrounds
    particles: {
        yellow: '#fff9c4',
        lime: '#d4edda',
        lavender: '#e2d5f1'
    }
};
const brandGradients = {
    // Main brand gradients
    cannabis: 'linear-gradient(135deg, #1b4332 0%, #267d47 50%, #95a99c 100%)',
    gold: 'linear-gradient(135deg, #d4a574 0%, #edd5a3 100%)',
    sage: 'linear-gradient(135deg, #95a99c 0%, #c4d2ca 100%)',
    // Context-based gradients
    hero: 'linear-gradient(135deg, #1b4332 0%, #163a2b 100%)',
    card: 'linear-gradient(135deg, #f8f6f0 0%, #ede8db 100%)',
    // Animated gradients for text
    dynamicText: {
        pineapple: 'linear-gradient(45deg, #ff8c00, #ffd700, #ff8c00)',
        gmo: 'linear-gradient(45deg, #4a90e2, #87ceeb, #4a90e2)',
        rosin: 'linear-gradient(45deg, #d4a574, #ff8c00, #d4a574)'
    }
};
const brandShadows = {
    // Soft shadows
    soft: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    large: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    // Brand-specific glows
    apothecaryGlow: '0 0 20px rgba(47, 184, 134, 0.3)',
    goldGlow: '0 0 20px rgba(212, 165, 116, 0.3)',
    // Dynamic shadows for tilt effects
    tiltShadow: (direction)=>{
        const shadows = {
            left: '-5px 5px 15px rgba(0, 0, 0, 0.2)',
            right: '5px 5px 15px rgba(0, 0, 0, 0.2)',
            up: '0 -5px 15px rgba(0, 0, 0, 0.2)',
            down: '0 5px 15px rgba(0, 0, 0, 0.2)'
        };
        return shadows[direction];
    }
};
const brandAnimations = {
    // Easing functions (Framer Motion format)
    easing: {
        smooth: [
            0.4,
            0,
            0.2,
            1
        ],
        bounce: [
            0.68,
            -0.55,
            0.265,
            1.55
        ],
        elastic: [
            0.175,
            0.885,
            0.32,
            1.275
        ]
    },
    // CSS easing functions (for CSS transitions)
    cssEasing: {
        smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    },
    // Duration presets
    duration: {
        fast: 200,
        normal: 300,
        slow: 500,
        verySlow: 800
    },
    // Common animation variants for Framer Motion
    fadeInUp: {
        initial: {
            opacity: 0,
            y: 40
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        }
    },
    scaleOnHover: {
        whileHover: {
            scale: 1.07,
            brightness: 1.1
        },
        transition: {
            duration: 0.2
        }
    },
    tiltEffect: {
        perspective: 1000,
        rotateRange: 10
    },
    typewriter: {
        charDelay: 50,
        cursorBlink: 1000
    }
};
const brandTypography = {
    fonts: {
        sans: 'var(--font-inter), Inter, system-ui, sans-serif',
        serif: 'var(--font-playfair), Playfair Display, Georgia, serif'
    },
    sizes: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
        '6xl': '3.75rem'
    },
    weights: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
    }
};
const brandSpacing = {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
    '5xl': '8rem'
};
const brandUtils = {
    // Get color with opacity
    withOpacity: (color, opacity)=>{
        if (color.startsWith('#')) {
            const hex = color.slice(1);
            const r = parseInt(hex.slice(0, 2), 16);
            const g = parseInt(hex.slice(2, 4), 16);
            const b = parseInt(hex.slice(4, 6), 16);
            return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }
        return color;
    },
    // Generate random terpene gradient
    randomTerpeneGradient: ()=>{
        const terpenes = Object.keys(terpeneColors);
        const randomTerpene = terpenes[Math.floor(Math.random() * terpenes.length)];
        return terpeneColors[randomTerpene].gradient;
    },
    // Get appropriate text color for background
    getTextColor: (backgroundColor)=>{
        // Simple light/dark detection - in production, use a proper contrast calculation
        const darkColors = [
            '#1b4332',
            '#163a2b',
            '#267d47',
            '#2d3436',
            '#212529'
        ];
        return darkColors.includes(backgroundColor) ? brandColors.cream[50] : brandColors.charcoal[800];
    }
};
const __TURBOPACK__default__export__ = {
    colors: brandColors,
    terpenes: terpeneColors,
    gradients: brandGradients,
    shadows: brandShadows,
    animations: brandAnimations,
    typography: brandTypography,
    spacing: brandSpacing,
    utils: brandUtils
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/hero/HeroVideoWithCTA.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
/**
 * HeroVideoWithCTA Component
 * 
 * A hero video component with call-to-action overlays and cannabis industry branding.
 * Features auto-playing video backgrounds with customizable overlays and CTAs.
 * 
 * @example
 * ```tsx
 * <HeroVideoWithCTA
 *   videoSrc="/videos/hero-cannabis.mp4"
 *   headline="Premium Cannabis Extracts"
 *   subtitle="Award-winning quality, lab-tested purity"
 *   primaryCTA="Shop Now"
 *   onPrimaryCTA={() => router.push('/products')}
 *   secondaryCTA="Learn More"
 *   onSecondaryCTA={() => router.push('/about')}
 * />
 * ```
 */ const HeroVideoWithCTA = ({ videoSrc, posterSrc, headline, subtitle, primaryCTA, onPrimaryCTA, secondaryCTA, onSecondaryCTA, autoplay = true, loop = true, muted = true, overlayOpacity = 0.4, className = '', style })=>{
    _s();
    const [isVideoLoaded, setIsVideoLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const videoRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HeroVideoWithCTA.useEffect": ()=>{
            const video = videoRef.current;
            if (video && autoplay) {
                video.play().catch({
                    "HeroVideoWithCTA.useEffect": ()=>{
                        // Auto-play failed, user interaction required
                        setIsPlaying(false);
                    }
                }["HeroVideoWithCTA.useEffect"]);
            }
        }
    }["HeroVideoWithCTA.useEffect"], [
        autoplay
    ]);
    const handleVideoLoad = ()=>{
        setIsVideoLoaded(true);
    };
    const handlePlayPause = ()=>{
        const video = videoRef.current;
        if (video) {
            if (video.paused) {
                video.play();
                setIsPlaying(true);
            } else {
                video.pause();
                setIsPlaying(false);
            }
        }
    };
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2,
                delayChildren: 0.3
            }
        }
    };
    const itemVariants = {
        hidden: {
            opacity: 0,
            y: 30
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        }
    };
    const scrollIndicatorVariants = {
        animate: {
            y: [
                0,
                10,
                0
            ],
            transition: {
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: `relative h-screen overflow-hidden bg-primary-800 ${className}`,
        style: style,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                        ref: videoRef,
                        autoPlay: autoplay,
                        muted: muted,
                        loop: loop,
                        playsInline: true,
                        poster: posterSrc,
                        onLoadedData: handleVideoLoad,
                        onPlay: ()=>setIsPlaying(true),
                        onPause: ()=>setIsPlaying(false),
                        className: `w-full h-full object-cover transition-opacity duration-1000 ${isVideoLoaded ? 'opacity-100' : 'opacity-0'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("source", {
                                src: videoSrc,
                                type: "video/mp4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                                lineNumber: 130,
                                columnNumber: 11
                            }, this),
                            "Your browser does not support the video tag."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                        lineNumber: 116,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-black transition-opacity duration-300",
                        style: {
                            opacity: overlayOpacity
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                        lineNumber: 135,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                lineNumber: 115,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10 h-full flex flex-col justify-center items-center text-center px-6 sm:px-8 lg:px-12",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: containerVariants,
                    initial: "hidden",
                    animate: "visible",
                    className: "max-w-4xl",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].h1, {
                            variants: itemVariants,
                            className: "text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-cream-50 leading-tight mb-6",
                            children: headline.split(' ').map((word, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].span, {
                                    variants: {
                                        hidden: {
                                            opacity: 0,
                                            y: 50
                                        },
                                        visible: {
                                            opacity: 1,
                                            y: 0,
                                            transition: {
                                                duration: 0.6,
                                                delay: index * 0.1,
                                                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                                            }
                                        }
                                    },
                                    className: "inline-block mr-3",
                                    children: word
                                }, index, false, {
                                    fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                                    lineNumber: 158,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                            lineNumber: 153,
                            columnNumber: 11
                        }, this),
                        subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].p, {
                            variants: itemVariants,
                            className: "text-xl sm:text-2xl md:text-3xl text-cream-200 mb-8 leading-relaxed max-w-3xl mx-auto",
                            children: subtitle
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                            lineNumber: 181,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            variants: itemVariants,
                            className: "flex flex-col sm:flex-row gap-4 justify-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                                    whileHover: {
                                        scale: 1.05
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    },
                                    onClick: onPrimaryCTA,
                                    className: "inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent bg-gold-500 text-primary-800 hover:bg-gold-400 focus:ring-gold-400",
                                    children: [
                                        primaryCTA,
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                            className: "ml-2 h-5 w-5",
                                            fill: "none",
                                            stroke: "currentColor",
                                            viewBox: "0 0 24 24",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                strokeLinecap: "round",
                                                strokeLinejoin: "round",
                                                strokeWidth: 2,
                                                d: "M9 5l7 7-7 7"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                                                lineNumber: 203,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                                    lineNumber: 195,
                                    columnNumber: 13
                                }, this),
                                secondaryCTA && onSecondaryCTA && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                                    whileHover: {
                                        scale: 1.05
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    },
                                    onClick: onSecondaryCTA,
                                    className: "inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent border-2 border-cream-50 text-cream-50 hover:bg-cream-50 hover:text-primary-800 focus:ring-cream-50",
                                    children: secondaryCTA
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                                    lineNumber: 209,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                            lineNumber: 190,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                    lineNumber: 146,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                lineNumber: 145,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-6 left-6 z-20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                    whileHover: {
                        scale: 1.1
                    },
                    whileTap: {
                        scale: 0.9
                    },
                    onClick: handlePlayPause,
                    className: "bg-black/50 hover:bg-black/70 text-white w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-200",
                    "aria-label": isPlaying ? 'Pause video' : 'Play video',
                    children: isPlaying ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "w-6 h-6",
                        fill: "currentColor",
                        viewBox: "0 0 20 20",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            fillRule: "evenodd",
                            d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z",
                            clipRule: "evenodd"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                            lineNumber: 233,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                        lineNumber: 232,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "w-6 h-6 ml-1",
                        fill: "currentColor",
                        viewBox: "0 0 20 20",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            fillRule: "evenodd",
                            d: "M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",
                            clipRule: "evenodd"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                            lineNumber: 237,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                        lineNumber: 236,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                    lineNumber: 224,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                lineNumber: 223,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: scrollIndicatorVariants,
                animate: "animate",
                className: "absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center text-cream-50",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm font-medium mb-2",
                            children: "Scroll"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                            lineNumber: 250,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-6 h-6",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M19 14l-7 7m0 0l-7-7m7 7V3"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                                lineNumber: 252,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                            lineNumber: 251,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                    lineNumber: 249,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                lineNumber: 244,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-0 left-0 right-0 z-20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 1440 120",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z",
                        fill: "var(--background)"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                        lineNumber: 260,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                    lineNumber: 259,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
                lineNumber: 258,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/hero/HeroVideoWithCTA.tsx",
        lineNumber: 110,
        columnNumber: 5
    }, this);
};
_s(HeroVideoWithCTA, "S5+97Ioa/Rx7JdgOMjPBSCqKYFk=");
_c = HeroVideoWithCTA;
const __TURBOPACK__default__export__ = HeroVideoWithCTA;
var _c;
__turbopack_context__.k.register(_c, "HeroVideoWithCTA");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/cards/AnimatedCardSlider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
/**
 * AnimatedCardSlider Component
 * 
 * An animated card slider for product showcases with smooth transitions and touch support.
 * Features auto-advance, navigation controls, and responsive design.
 * 
 * @example
 * ```tsx
 * <AnimatedCardSlider
 *   products={products}
 *   visibleCards={3}
 *   autoAdvance={5000}
 *   touchEnabled={true}
 *   onCardClick={(product) => router.push(`/products/${product.id}`)}
 * />
 * ```
 */ const AnimatedCardSlider = ({ products, visibleCards = 3, autoAdvance = 4000, touchEnabled = true, showDots = true, showArrows = true, onCardClick, className = '', style })=>{
    _s();
    const [currentIndex, setCurrentIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [touchStart, setTouchStart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [touchEnd, setTouchEnd] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const intervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const maxIndex = Math.max(0, products.length - visibleCards);
    // Auto-advance functionality
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnimatedCardSlider.useEffect": ()=>{
            if (autoAdvance && !isHovered && products.length > visibleCards) {
                intervalRef.current = setInterval({
                    "AnimatedCardSlider.useEffect": ()=>{
                        setCurrentIndex({
                            "AnimatedCardSlider.useEffect": (prev)=>prev >= maxIndex ? 0 : prev + 1
                        }["AnimatedCardSlider.useEffect"]);
                    }
                }["AnimatedCardSlider.useEffect"], autoAdvance);
            }
            return ({
                "AnimatedCardSlider.useEffect": ()=>{
                    if (intervalRef.current) {
                        clearInterval(intervalRef.current);
                    }
                }
            })["AnimatedCardSlider.useEffect"];
        }
    }["AnimatedCardSlider.useEffect"], [
        autoAdvance,
        isHovered,
        maxIndex,
        products.length,
        visibleCards
    ]);
    // Touch handlers
    const handleTouchStart = (e)=>{
        if (!touchEnabled) return;
        setTouchStart(e.targetTouches[0].clientX);
    };
    const handleTouchMove = (e)=>{
        if (!touchEnabled) return;
        setTouchEnd(e.targetTouches[0].clientX);
    };
    const handleTouchEnd = ()=>{
        if (!touchEnabled || !touchStart || !touchEnd) return;
        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > 50;
        const isRightSwipe = distance < -50;
        if (isLeftSwipe) {
            nextSlide();
        } else if (isRightSwipe) {
            prevSlide();
        }
    };
    const goToSlide = (index)=>{
        setCurrentIndex(Math.max(0, Math.min(index, maxIndex)));
    };
    const nextSlide = ()=>{
        setCurrentIndex((prev)=>prev >= maxIndex ? 0 : prev + 1);
    };
    const prevSlide = ()=>{
        setCurrentIndex((prev)=>prev <= 0 ? maxIndex : prev - 1);
    };
    const cardVariants = {
        hidden: {
            opacity: 0,
            scale: 0.8,
            y: 20
        },
        visible: {
            opacity: 1,
            scale: 1,
            y: 0
        },
        hover: {
            y: -8,
            scale: 1.02
        }
    };
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2
            }
        }
    };
    const formatPrice = (price)=>{
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            margin: '-50px'
        },
        className: `relative ${className}`,
        style: style,
        onMouseEnter: ()=>setIsHovered(true),
        onMouseLeave: ()=>setIsHovered(false),
        onTouchStart: handleTouchStart,
        onTouchMove: handleTouchMove,
        onTouchEnd: handleTouchEnd,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "overflow-hidden",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "flex transition-transform duration-500 ease-out",
                    style: {
                        transform: `translateX(-${currentIndex * (100 / visibleCards)}%)`,
                        gap: '24px'
                    },
                    children: products.map((product, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            variants: cardVariants,
                            whileHover: "hover",
                            className: "flex-shrink-0 cursor-pointer",
                            style: {
                                width: `calc(${100 / visibleCards}% - ${24 * (visibleCards - 1) / visibleCards}px)`
                            },
                            onClick: ()=>onCardClick?.(product),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-cream-50 rounded-xl overflow-hidden group",
                                style: {
                                    boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].soft
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative h-48 bg-gradient-to-br from-primary-100 to-sage-100",
                                        children: [
                                            product.image ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: product.image,
                                                alt: product.name,
                                                fill: true,
                                                className: "object-cover group-hover:scale-105 transition-transform duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 158,
                                                columnNumber: 21
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-full h-full flex items-center justify-center",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        className: "w-8 h-8 text-primary-600",
                                                        fill: "currentColor",
                                                        viewBox: "0 0 20 20",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M10 2L3 7v11h14V7l-7-5z"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                            lineNumber: 168,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                        lineNumber: 167,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                    lineNumber: 166,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 165,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute top-3 right-3",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs font-semibold px-2 py-1 rounded-full text-cream-50",
                                                    style: {
                                                        backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600]
                                                    },
                                                    children: product.category
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                    lineNumber: 176,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 175,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 185,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                        lineNumber: 156,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200",
                                                children: product.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 190,
                                                columnNumber: 19
                                            }, this),
                                            product.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 mb-4 line-clamp-2",
                                                children: product.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 195,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-lg font-bold text-primary-600",
                                                        children: formatPrice(product.price)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                        lineNumber: 202,
                                                        columnNumber: 21
                                                    }, this),
                                                    product.strain && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",
                                                        children: product.strain
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                        lineNumber: 207,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 201,
                                                columnNumber: 19
                                            }, this),
                                            (product.thc || product.cbd) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex gap-2 mb-4",
                                                children: [
                                                    product.thc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded",
                                                        children: [
                                                            "THC: ",
                                                            product.thc,
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                        lineNumber: 217,
                                                        columnNumber: 25
                                                    }, this),
                                                    product.cbd && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded",
                                                        children: [
                                                            "CBD: ",
                                                            product.cbd,
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                        lineNumber: 222,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 215,
                                                columnNumber: 21
                                            }, this),
                                            product.effects && product.effects.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex flex-wrap gap-1",
                                                children: product.effects.slice(0, 3).map((effect, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded",
                                                        children: effect
                                                    }, idx, false, {
                                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                        lineNumber: 233,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                                lineNumber: 231,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                        lineNumber: 189,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                lineNumber: 153,
                                columnNumber: 15
                            }, this)
                        }, product.id, false, {
                            fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                            lineNumber: 145,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                    lineNumber: 137,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                lineNumber: 136,
                columnNumber: 7
            }, this),
            showArrows && products.length > visibleCards && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                        whileHover: {
                            scale: 1.1
                        },
                        whileTap: {
                            scale: 0.9
                        },
                        onClick: prevSlide,
                        className: "absolute left-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 z-10",
                        style: {
                            boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].medium
                        },
                        "aria-label": "Previous slide",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-5 h-5",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M15 19l-7-7 7-7"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                lineNumber: 261,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                            lineNumber: 260,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                        lineNumber: 252,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                        whileHover: {
                            scale: 1.1
                        },
                        whileTap: {
                            scale: 0.9
                        },
                        onClick: nextSlide,
                        className: "absolute right-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 z-10",
                        style: {
                            boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].medium
                        },
                        "aria-label": "Next slide",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-5 h-5",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M9 5l7 7-7 7"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                                lineNumber: 274,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                            lineNumber: 273,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                        lineNumber: 265,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true),
            showDots && products.length > visibleCards && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center mt-6 space-x-2",
                children: Array.from({
                    length: maxIndex + 1
                }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                        whileHover: {
                            scale: 1.2
                        },
                        whileTap: {
                            scale: 0.8
                        },
                        onClick: ()=>goToSlide(index),
                        className: `w-3 h-3 rounded-full transition-colors duration-200 ${index === currentIndex ? 'bg-primary-600' : 'bg-cream-300 hover:bg-primary-300'}`,
                        "aria-label": `Go to slide ${index + 1}`
                    }, index, false, {
                        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                        lineNumber: 284,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                lineNumber: 282,
                columnNumber: 9
            }, this),
            autoAdvance && !isHovered && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-0 left-0 right-0 h-1 bg-cream-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "h-full bg-primary-600",
                    initial: {
                        width: '0%'
                    },
                    animate: {
                        width: '100%'
                    },
                    transition: {
                        duration: autoAdvance / 1000,
                        ease: 'linear',
                        repeat: Infinity
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                    lineNumber: 303,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
                lineNumber: 302,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/cards/AnimatedCardSlider.tsx",
        lineNumber: 122,
        columnNumber: 5
    }, this);
};
_s(AnimatedCardSlider, "JocWcT5Rbg4qLxEjJJ8Siz0VF2k=");
_c = AnimatedCardSlider;
const __TURBOPACK__default__export__ = AnimatedCardSlider;
var _c;
__turbopack_context__.k.register(_c, "AnimatedCardSlider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/grids/TabbedProductGrid.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
/**
 * TabbedProductGrid Component
 * 
 * A tabbed grid system for organizing products by categories (flower, extracts, edibles).
 * Features smooth animations, pagination, and responsive design.
 * 
 * @example
 * ```tsx
 * <TabbedProductGrid
 *   products={products}
 *   categories={[
 *     { id: 'flower', name: 'Flower', icon: <FlowerIcon /> },
 *     { id: 'extract', name: 'Extracts', icon: <ExtractIcon /> }
 *   ]}
 *   columns={3}
 *   itemsPerPage={9}
 *   onProductClick={(product) => router.push(`/products/${product.id}`)}
 * />
 * ```
 */ const TabbedProductGrid = ({ products, categories, defaultCategory, columns = 3, itemsPerPage = 9, showPagination = true, onProductClick, className = '', style })=>{
    _s();
    const [activeCategory, setActiveCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultCategory || categories[0]?.id || 'all');
    const [currentPage, setCurrentPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    // Filter products by active category
    const filteredProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TabbedProductGrid.useMemo[filteredProducts]": ()=>{
            if (activeCategory === 'all') return products;
            return products.filter({
                "TabbedProductGrid.useMemo[filteredProducts]": (product)=>product.category === activeCategory
            }["TabbedProductGrid.useMemo[filteredProducts]"]);
        }
    }["TabbedProductGrid.useMemo[filteredProducts]"], [
        products,
        activeCategory
    ]);
    // Paginate products
    const paginatedProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TabbedProductGrid.useMemo[paginatedProducts]": ()=>{
            if (!showPagination) return filteredProducts;
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            return filteredProducts.slice(startIndex, endIndex);
        }
    }["TabbedProductGrid.useMemo[paginatedProducts]"], [
        filteredProducts,
        currentPage,
        itemsPerPage,
        showPagination
    ]);
    const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
    // Reset to first page when category changes
    const handleCategoryChange = (categoryId)=>{
        setActiveCategory(categoryId);
        setCurrentPage(1);
    };
    const handlePageChange = (page)=>{
        setCurrentPage(page);
    };
    const formatPrice = (price)=>{
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };
    const getCategoryIcon = (categoryId)=>{
        const category = categories.find((cat)=>cat.id === categoryId);
        return category?.icon || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            className: "w-4 h-4",
            fill: "currentColor",
            viewBox: "0 0 20 20",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M10 2L3 7v11h14V7l-7-5z"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                lineNumber: 83,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
            lineNumber: 82,
            columnNumber: 7
        }, this);
    };
    const tabVariants = {
        hidden: {
            opacity: 0,
            y: 10
        },
        visible: {
            opacity: 1,
            y: 0
        }
    };
    const gridVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2
            }
        }
    };
    const cardVariants = {
        hidden: {
            opacity: 0,
            scale: 0.8,
            y: 20
        },
        visible: {
            opacity: 1,
            scale: 1,
            y: 0,
            transition: {
                duration: 0.4,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        },
        hover: {
            y: -8,
            scale: 1.02
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `tabbed-product-grid ${className}`,
        style: style,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                    className: "flex flex-wrap gap-2 justify-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                            variants: tabVariants,
                            initial: "hidden",
                            animate: "visible",
                            onClick: ()=>handleCategoryChange('all'),
                            className: `px-6 py-3 font-medium text-sm rounded-full border transition-all duration-200 flex items-center gap-2 ${activeCategory === 'all' ? 'bg-primary-600 text-cream-50 border-primary-600' : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "w-4 h-4",
                                    fill: "currentColor",
                                    viewBox: "0 0 20 20",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                        lineNumber: 136,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                    lineNumber: 135,
                                    columnNumber: 13
                                }, this),
                                "All Products",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full",
                                    children: products.length
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                    lineNumber: 139,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                            lineNumber: 124,
                            columnNumber: 11
                        }, this),
                        categories.map((category)=>{
                            const categoryProducts = products.filter((p)=>p.category === category.id);
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                                variants: tabVariants,
                                initial: "hidden",
                                animate: "visible",
                                onClick: ()=>handleCategoryChange(category.id),
                                className: `px-6 py-3 font-medium text-sm rounded-full border transition-all duration-200 flex items-center gap-2 ${activeCategory === category.id ? 'bg-primary-600 text-cream-50 border-primary-600' : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'}`,
                                children: [
                                    category.icon,
                                    category.name,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full",
                                        children: categoryProducts.length
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                        lineNumber: 162,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, category.id, true, {
                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                lineNumber: 148,
                                columnNumber: 15
                            }, this);
                        })
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                    lineNumber: 122,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                lineNumber: 121,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                mode: "wait",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: gridVariants,
                    initial: "hidden",
                    animate: "visible",
                    exit: "hidden",
                    className: `grid gap-6 ${columns === 1 ? 'grid-cols-1' : columns === 2 ? 'grid-cols-1 md:grid-cols-2' : columns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : columns === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'}`,
                    children: paginatedProducts.map((product)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            variants: cardVariants,
                            whileHover: "hover",
                            className: "cursor-pointer",
                            onClick: ()=>onProductClick?.(product),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-cream-50 rounded-xl overflow-hidden group",
                                style: {
                                    boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].soft
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative h-48 bg-gradient-to-br from-primary-100 to-sage-100",
                                        children: [
                                            product.image ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: product.image,
                                                alt: product.name,
                                                fill: true,
                                                className: "object-cover group-hover:scale-105 transition-transform duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 202,
                                                columnNumber: 21
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-full h-full flex items-center justify-center",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center",
                                                    children: getCategoryIcon(product.category)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                    lineNumber: 210,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 209,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute top-3 right-3",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize",
                                                    style: {
                                                        backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600]
                                                    },
                                                    children: product.category
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                    lineNumber: 218,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 217,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 227,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                        lineNumber: 200,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200",
                                                children: product.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 232,
                                                columnNumber: 19
                                            }, this),
                                            product.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 mb-4 line-clamp-2",
                                                children: product.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 237,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-lg font-bold text-primary-600",
                                                        children: formatPrice(product.price)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                        lineNumber: 244,
                                                        columnNumber: 21
                                                    }, this),
                                                    product.strain && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",
                                                        children: product.strain
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                        lineNumber: 249,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 243,
                                                columnNumber: 19
                                            }, this),
                                            (product.thc || product.cbd) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex gap-2 mb-4",
                                                children: [
                                                    product.thc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded",
                                                        children: [
                                                            "THC: ",
                                                            product.thc,
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                        lineNumber: 259,
                                                        columnNumber: 25
                                                    }, this),
                                                    product.cbd && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded",
                                                        children: [
                                                            "CBD: ",
                                                            product.cbd,
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                        lineNumber: 264,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 257,
                                                columnNumber: 21
                                            }, this),
                                            product.effects && product.effects.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex flex-wrap gap-1",
                                                children: product.effects.slice(0, 3).map((effect, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded",
                                                        children: effect
                                                    }, idx, false, {
                                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                        lineNumber: 275,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                                lineNumber: 273,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                        lineNumber: 231,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                lineNumber: 195,
                                columnNumber: 15
                            }, this)
                        }, product.id, false, {
                            fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                            lineNumber: 188,
                            columnNumber: 13
                        }, this))
                }, activeCategory, false, {
                    fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                    lineNumber: 173,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                lineNumber: 172,
                columnNumber: 7
            }, this),
            showPagination && totalPages > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center items-center gap-2 mt-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handlePageChange(currentPage - 1),
                        disabled: currentPage === 1,
                        className: "px-3 py-2 rounded-md border border-cream-300 disabled:opacity-50 disabled:cursor-not-allowed hover:border-primary-300 transition-colors",
                        children: "Previous"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                        lineNumber: 294,
                        columnNumber: 11
                    }, this),
                    Array.from({
                        length: totalPages
                    }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>handlePageChange(page),
                            className: `px-3 py-2 rounded-md border transition-colors ${page === currentPage ? 'bg-primary-600 text-cream-50 border-primary-600' : 'border-cream-300 hover:border-primary-300'}`,
                            children: page
                        }, page, false, {
                            fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                            lineNumber: 303,
                            columnNumber: 13
                        }, this)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handlePageChange(currentPage + 1),
                        disabled: currentPage === totalPages,
                        className: "px-3 py-2 rounded-md border border-cream-300 disabled:opacity-50 disabled:cursor-not-allowed hover:border-primary-300 transition-colors",
                        children: "Next"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                        lineNumber: 316,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                lineNumber: 293,
                columnNumber: 9
            }, this),
            paginatedProducts.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center py-12",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-8 h-8 text-gray-400",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                                lineNumber: 331,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                            lineNumber: 330,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                        lineNumber: 329,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 mb-2",
                        children: "No products found"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                        lineNumber: 334,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-500",
                        children: "Try selecting a different category."
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                        lineNumber: 335,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
                lineNumber: 328,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/grids/TabbedProductGrid.tsx",
        lineNumber: 119,
        columnNumber: 5
    }, this);
};
_s(TabbedProductGrid, "pvX9RwbW3IFJxdfob76yl6xC4Gs=");
_c = TabbedProductGrid;
const __TURBOPACK__default__export__ = TabbedProductGrid;
var _c;
__turbopack_context__.k.register(_c, "TabbedProductGrid");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/grids/MasonryProductShowcase.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
/**
 * MasonryProductShowcase Component
 * 
 * A masonry/carousel layout for dynamic product displays with filtering capabilities.
 * Features responsive masonry grid with smooth animations and filter controls.
 * 
 * @example
 * ```tsx
 * <MasonryProductShowcase
 *   products={products}
 *   columns={3}
 *   gap={20}
 *   carouselMode={false}
 *   filters={[
 *     { id: 'category', name: 'Category', value: 'flower' },
 *     { id: 'price', name: 'Price Range', value: 'under-50' }
 *   ]}
 *   onProductClick={(product) => router.push(`/products/${product.id}`)}
 * />
 * ```
 */ const MasonryProductShowcase = ({ products, columns = 3, gap = 20, carouselMode = false, filters = [], onProductClick, className = '', style })=>{
    _s();
    const [activeFilters, setActiveFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [currentSlide, setCurrentSlide] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Filter products based on active filters
    const filteredProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MasonryProductShowcase.useMemo[filteredProducts]": ()=>{
            return products.filter({
                "MasonryProductShowcase.useMemo[filteredProducts]": (product)=>{
                    return Object.entries(activeFilters).every({
                        "MasonryProductShowcase.useMemo[filteredProducts]": ([filterId, filterValue])=>{
                            if (!filterValue) return true;
                            switch(filterId){
                                case 'category':
                                    return product.category === filterValue;
                                case 'price':
                                    if (filterValue === 'under-25') return product.price < 25;
                                    if (filterValue === '25-50') return product.price >= 25 && product.price <= 50;
                                    if (filterValue === '50-100') return product.price > 50 && product.price <= 100;
                                    if (filterValue === 'over-100') return product.price > 100;
                                    return true;
                                case 'effects':
                                    return product.effects?.includes(filterValue);
                                default:
                                    return true;
                            }
                        }
                    }["MasonryProductShowcase.useMemo[filteredProducts]"]);
                }
            }["MasonryProductShowcase.useMemo[filteredProducts]"]);
        }
    }["MasonryProductShowcase.useMemo[filteredProducts]"], [
        products,
        activeFilters
    ]);
    // Handle filter changes
    const handleFilterChange = (filterId, value)=>{
        setActiveFilters((prev)=>({
                ...prev,
                [filterId]: prev[filterId] === value ? '' : value
            }));
    };
    // Clear all filters
    const clearFilters = ()=>{
        setActiveFilters({});
    };
    // Carousel navigation
    const nextSlide = ()=>{
        if (carouselMode) {
            setCurrentSlide((prev)=>(prev + 1) % Math.ceil(filteredProducts.length / columns));
        }
    };
    const prevSlide = ()=>{
        if (carouselMode) {
            setCurrentSlide((prev)=>prev === 0 ? Math.ceil(filteredProducts.length / columns) - 1 : prev - 1);
        }
    };
    // Auto-advance carousel
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryProductShowcase.useEffect": ()=>{
            if (carouselMode) {
                const interval = setInterval(nextSlide, 5000);
                return ({
                    "MasonryProductShowcase.useEffect": ()=>clearInterval(interval)
                })["MasonryProductShowcase.useEffect"];
            }
        }
    }["MasonryProductShowcase.useEffect"], [
        carouselMode
    ]);
    const formatPrice = (price)=>{
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2
            }
        }
    };
    const cardVariants = {
        hidden: {
            opacity: 0,
            scale: 0.8,
            y: 20
        },
        visible: {
            opacity: 1,
            scale: 1,
            y: 0,
            transition: {
                duration: 0.4,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        },
        hover: {
            y: -8,
            scale: 1.02
        }
    };
    // Get random height for masonry effect
    const getRandomHeight = (index)=>{
        const heights = [
            200,
            250,
            300,
            350
        ];
        return heights[index % heights.length];
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `masonry-product-showcase ${className}`,
        style: style,
        children: [
            filters.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-4 items-center justify-between mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-semibold text-charcoal-800",
                                children: "Filter Products"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 146,
                                columnNumber: 13
                            }, this),
                            Object.keys(activeFilters).some((key)=>activeFilters[key]) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: clearFilters,
                                className: "text-sm text-primary-600 hover:text-primary-700 font-medium",
                                children: "Clear All Filters"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 148,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 145,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-3",
                        children: filters.map((filter)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>handleFilterChange(filter.id, filter.value),
                                    className: `px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 ${activeFilters[filter.id] === filter.value ? 'bg-primary-600 text-cream-50 border-primary-600' : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'}`,
                                    children: filter.name
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                    lineNumber: 160,
                                    columnNumber: 17
                                }, this)
                            }, filter.id, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 159,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 157,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                lineNumber: 144,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-sm text-gray-600",
                    children: [
                        "Showing ",
                        filteredProducts.length,
                        " of ",
                        products.length,
                        " products"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                    lineNumber: 178,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                lineNumber: 177,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: containerRef,
                className: "relative",
                children: carouselMode ? // Carousel Mode
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "flex transition-transform duration-500 ease-out",
                            style: {
                                transform: `translateX(-${currentSlide * 100}%)`,
                                gap: `${gap}px`
                            },
                            children: Array.from({
                                length: Math.ceil(filteredProducts.length / columns)
                            }).map((_, slideIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-shrink-0 w-full grid gap-4",
                                    style: {
                                        gridTemplateColumns: `repeat(${columns}, 1fr)`,
                                        gap: `${gap}px`
                                    },
                                    children: filteredProducts.slice(slideIndex * columns, (slideIndex + 1) * columns).map((product, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                            variants: cardVariants,
                                            whileHover: "hover",
                                            className: "cursor-pointer",
                                            onClick: ()=>onProductClick?.(product),
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProductCard, {
                                                product: product,
                                                height: getRandomHeight(slideIndex * columns + index)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                                lineNumber: 214,
                                                columnNumber: 25
                                            }, this)
                                        }, product.id, false, {
                                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                            lineNumber: 207,
                                            columnNumber: 23
                                        }, this))
                                }, slideIndex, false, {
                                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                    lineNumber: 196,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                            lineNumber: 188,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center items-center gap-4 mt-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: prevSlide,
                                    className: "p-2 rounded-full bg-cream-50 hover:bg-primary-600 hover:text-cream-50 transition-colors",
                                    style: {
                                        boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].medium
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-5 h-5",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M15 19l-7-7 7-7"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                            lineNumber: 232,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                        lineNumber: 231,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                    lineNumber: 226,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex gap-2",
                                    children: Array.from({
                                        length: Math.ceil(filteredProducts.length / columns)
                                    }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setCurrentSlide(index),
                                            className: `w-3 h-3 rounded-full transition-colors ${index === currentSlide ? 'bg-primary-600' : 'bg-cream-300'}`
                                        }, index, false, {
                                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                            lineNumber: 238,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                    lineNumber: 236,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: nextSlide,
                                    className: "p-2 rounded-full bg-cream-50 hover:bg-primary-600 hover:text-cream-50 transition-colors",
                                    style: {
                                        boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].medium
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-5 h-5",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M9 5l7 7-7 7"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                            lineNumber: 254,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                        lineNumber: 253,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                    lineNumber: 248,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                            lineNumber: 225,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                    lineNumber: 187,
                    columnNumber: 11
                }, this) : // Masonry Mode
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: containerVariants,
                    initial: "hidden",
                    animate: "visible",
                    className: "columns-1 md:columns-2 lg:columns-3 xl:columns-4 2xl:columns-5",
                    style: {
                        columnGap: `${gap}px`,
                        columnCount: columns
                    },
                    children: filteredProducts.map((product, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            variants: cardVariants,
                            whileHover: "hover",
                            className: "cursor-pointer break-inside-avoid mb-4",
                            onClick: ()=>onProductClick?.(product),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProductCard, {
                                product: product,
                                height: getRandomHeight(index)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 279,
                                columnNumber: 17
                            }, this)
                        }, product.id, false, {
                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                            lineNumber: 272,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                    lineNumber: 261,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                lineNumber: 184,
                columnNumber: 7
            }, this),
            filteredProducts.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center py-12",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-8 h-8 text-gray-400",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 294,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                            lineNumber: 293,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 292,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 mb-2",
                        children: "No products found"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 297,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-500",
                        children: "Try adjusting your filters or search terms."
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 298,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                lineNumber: 291,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
        lineNumber: 141,
        columnNumber: 5
    }, this);
};
_s(MasonryProductShowcase, "NAUMPSFvwXJl5ikbMSwWOcrhnnM=");
_c = MasonryProductShowcase;
const ProductCard = ({ product, height })=>{
    const formatPrice = (price)=>{
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-cream-50 rounded-xl overflow-hidden group",
        style: {
            boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].soft
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative bg-gradient-to-br from-primary-100 to-sage-100",
                style: {
                    height: `${height}px`
                },
                children: [
                    product.image ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        src: product.image,
                        alt: product.name,
                        fill: true,
                        className: "object-cover group-hover:scale-105 transition-transform duration-300"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 330,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full h-full flex items-center justify-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-8 h-8 text-primary-600",
                                fill: "currentColor",
                                viewBox: "0 0 20 20",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M10 2L3 7v11h14V7l-7-5z"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                    lineNumber: 340,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 339,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                            lineNumber: 338,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 337,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-3 right-3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize",
                            style: {
                                backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600]
                            },
                            children: product.category
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                            lineNumber: 348,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 347,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 357,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                lineNumber: 325,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200",
                        children: product.name
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 362,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-lg font-bold text-primary-600",
                                children: formatPrice(product.price)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 367,
                                columnNumber: 11
                            }, this),
                            product.strain && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",
                                children: product.strain
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 372,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 366,
                        columnNumber: 9
                    }, this),
                    (product.thc || product.cbd) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-2 mb-3",
                        children: [
                            product.thc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded",
                                children: [
                                    "THC: ",
                                    product.thc,
                                    "%"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 382,
                                columnNumber: 15
                            }, this),
                            product.cbd && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded",
                                children: [
                                    "CBD: ",
                                    product.cbd,
                                    "%"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 387,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 380,
                        columnNumber: 11
                    }, this),
                    product.effects && product.effects.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-1",
                        children: product.effects.slice(0, 2).map((effect, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded",
                                children: effect
                            }, idx, false, {
                                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                                lineNumber: 398,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                        lineNumber: 396,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
                lineNumber: 361,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/grids/MasonryProductShowcase.tsx",
        lineNumber: 320,
        columnNumber: 5
    }, this);
};
_c1 = ProductCard;
const __TURBOPACK__default__export__ = MasonryProductShowcase;
var _c, _c1;
__turbopack_context__.k.register(_c, "MasonryProductShowcase");
__turbopack_context__.k.register(_c1, "ProductCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/effects/GlitchTextEffect.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-intersection-observer/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
/**
 * GlitchTextEffect Component
 * 
 * Creates glitch text effects for modern, edgy branding elements.
 * Features customizable intensity, colors, and animation triggers.
 * 
 * @example
 * ```tsx
 * <GlitchTextEffect
 *   text="APOTHECARY FARMS"
 *   intensity="high"
 *   trigger="hover"
 *   colors={['#2FB886', '#ff0080', '#00ff80']}
 *   fontSize="4rem"
 * />
 * ```
 */ const GlitchTextEffect = ({ text, intensity = 'medium', trigger = 'hover', colors = [
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].apothecary,
    '#ff0080',
    '#00ff80'
], fontSize = '2rem', fontWeight = 700, className = '', style })=>{
    _s();
    const [isGlitching, setIsGlitching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(trigger === 'continuous');
    const [glitchText, setGlitchText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(text);
    const intervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const timeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [ref, inView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"])({
        threshold: 0.1,
        triggerOnce: trigger === 'scroll'
    });
    // Glitch characters for text corruption
    const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?~`';
    const numbers = '0123456789';
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    // Get intensity settings
    const getIntensitySettings = ()=>{
        switch(intensity){
            case 'low':
                return {
                    glitchDuration: 100,
                    glitchInterval: 2000,
                    corruptionChance: 0.1,
                    maxCorruptedChars: 2
                };
            case 'medium':
                return {
                    glitchDuration: 200,
                    glitchInterval: 1500,
                    corruptionChance: 0.2,
                    maxCorruptedChars: 4
                };
            case 'high':
                return {
                    glitchDuration: 300,
                    glitchInterval: 1000,
                    corruptionChance: 0.3,
                    maxCorruptedChars: 6
                };
            default:
                return {
                    glitchDuration: 200,
                    glitchInterval: 1500,
                    corruptionChance: 0.2,
                    maxCorruptedChars: 4
                };
        }
    };
    const settings = getIntensitySettings();
    // Generate corrupted text
    const generateGlitchedText = ()=>{
        const chars = text.split('');
        const corruptedIndices = new Set();
        // Randomly select characters to corrupt
        const numToCorrupt = Math.min(Math.floor(Math.random() * settings.maxCorruptedChars) + 1, chars.length);
        while(corruptedIndices.size < numToCorrupt){
            const randomIndex = Math.floor(Math.random() * chars.length);
            if (chars[randomIndex] !== ' ') {
                corruptedIndices.add(randomIndex);
            }
        }
        // Replace selected characters with glitch characters
        corruptedIndices.forEach((index)=>{
            const charSets = [
                glitchChars,
                numbers,
                letters
            ];
            const randomSet = charSets[Math.floor(Math.random() * charSets.length)];
            chars[index] = randomSet[Math.floor(Math.random() * randomSet.length)];
        });
        return chars.join('');
    };
    // Start glitch effect
    const startGlitch = ()=>{
        if (intervalRef.current) return;
        setIsGlitching(true);
        intervalRef.current = setInterval(()=>{
            setGlitchText(generateGlitchedText());
            // Reset to original text after glitch duration
            timeoutRef.current = setTimeout(()=>{
                setGlitchText(text);
            }, settings.glitchDuration);
        }, settings.glitchInterval);
    };
    // Stop glitch effect
    const stopGlitch = ()=>{
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
        setIsGlitching(false);
        setGlitchText(text);
    };
    // Handle trigger effects
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlitchTextEffect.useEffect": ()=>{
            if (trigger === 'continuous') {
                startGlitch();
            } else if (trigger === 'scroll' && inView) {
                startGlitch();
                // Stop after a few cycles
                setTimeout(stopGlitch, 5000);
            }
            return ({
                "GlitchTextEffect.useEffect": ()=>{
                    stopGlitch();
                }
            })["GlitchTextEffect.useEffect"];
        }
    }["GlitchTextEffect.useEffect"], [
        trigger,
        inView
    ]);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlitchTextEffect.useEffect": ()=>{
            return ({
                "GlitchTextEffect.useEffect": ()=>{
                    stopGlitch();
                }
            })["GlitchTextEffect.useEffect"];
        }
    }["GlitchTextEffect.useEffect"], []);
    const handleMouseEnter = ()=>{
        if (trigger === 'hover') {
            startGlitch();
        }
    };
    const handleMouseLeave = ()=>{
        if (trigger === 'hover') {
            stopGlitch();
        }
    };
    // Generate CSS for glitch effect
    const glitchStyles = {
        position: 'relative',
        fontSize,
        fontWeight,
        fontFamily: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandTypography"].fonts.sans,
        color: colors[0],
        cursor: trigger === 'hover' ? 'pointer' : 'default',
        ...style
    };
    const beforeAfterStyles = {
        content: `"${glitchText}"`,
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'transparent'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        ref: ref,
        className: `glitch-text-effect ${className}`,
        style: glitchStyles,
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        initial: {
            opacity: 0
        },
        animate: {
            opacity: 1
        },
        transition: {
            duration: 0.5
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "relative z-10",
                children: glitchText
            }, void 0, false, {
                fileName: "[project]/src/components/ui/effects/GlitchTextEffect.tsx",
                lineNumber: 212,
                columnNumber: 7
            }, this),
            isGlitching && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].span, {
                        className: "absolute top-0 left-0 z-0",
                        style: {
                            color: colors[1] || '#ff0080',
                            clipPath: 'polygon(0 0, 100% 0, 100% 45%, 0 45%)'
                        },
                        animate: {
                            x: [
                                -2,
                                2,
                                -1,
                                1,
                                0
                            ],
                            y: [
                                0,
                                -1,
                                1,
                                0,
                                -1
                            ]
                        },
                        transition: {
                            duration: 0.1,
                            repeat: Infinity,
                            repeatType: 'mirror'
                        },
                        children: glitchText
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/GlitchTextEffect.tsx",
                        lineNumber: 218,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].span, {
                        className: "absolute top-0 left-0 z-0",
                        style: {
                            color: colors[2] || '#00ff80',
                            clipPath: 'polygon(0 55%, 100% 55%, 100% 100%, 0 100%)'
                        },
                        animate: {
                            x: [
                                1,
                                -2,
                                2,
                                -1,
                                0
                            ],
                            y: [
                                1,
                                0,
                                -1,
                                1,
                                0
                            ]
                        },
                        transition: {
                            duration: 0.1,
                            repeat: Infinity,
                            repeatType: 'mirror',
                            delay: 0.05
                        },
                        children: glitchText
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/GlitchTextEffect.tsx",
                        lineNumber: 238,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 z-20 pointer-events-none",
                        style: {
                            background: `repeating-linear-gradient(
                0deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.03) 2px,
                rgba(255, 255, 255, 0.03) 4px
              )`
                        },
                        animate: {
                            opacity: [
                                0.5,
                                1,
                                0.5
                            ]
                        },
                        transition: {
                            duration: 0.1,
                            repeat: Infinity,
                            repeatType: 'mirror'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/GlitchTextEffect.tsx",
                        lineNumber: 259,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 z-10 pointer-events-none",
                        style: {
                            background: `radial-gradient(circle, transparent 50%, rgba(255, 255, 255, 0.1) 50%)`,
                            backgroundSize: '4px 4px'
                        },
                        animate: {
                            backgroundPosition: [
                                '0px 0px',
                                '4px 4px',
                                '0px 4px',
                                '4px 0px'
                            ],
                            opacity: [
                                0,
                                0.3,
                                0,
                                0.2,
                                0
                            ]
                        },
                        transition: {
                            duration: 0.2,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/GlitchTextEffect.tsx",
                        lineNumber: 281,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true),
            isGlitching && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute inset-0 z-0",
                style: {
                    color: colors[0],
                    filter: 'blur(4px)',
                    opacity: 0.5
                },
                animate: {
                    opacity: [
                        0.3,
                        0.7,
                        0.3
                    ]
                },
                transition: {
                    duration: 0.2,
                    repeat: Infinity,
                    repeatType: 'mirror'
                },
                children: glitchText
            }, void 0, false, {
                fileName: "[project]/src/components/ui/effects/GlitchTextEffect.tsx",
                lineNumber: 302,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/effects/GlitchTextEffect.tsx",
        lineNumber: 201,
        columnNumber: 5
    }, this);
};
_s(GlitchTextEffect, "Ly0mlubhmVnJ5HIypQC6ldZrBtw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"]
    ];
});
_c = GlitchTextEffect;
const __TURBOPACK__default__export__ = GlitchTextEffect;
var _c;
__turbopack_context__.k.register(_c, "GlitchTextEffect");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/cards/FilterableStrainCards.tsx [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/src/components/ui/cards/FilterableStrainCards.tsx'

Unexpected token `div`. Expected jsx identifier`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),
"[project]/src/components/ui/layout/VerticalTimeline.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-intersection-observer/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
/**
 * VerticalTimeline Component
 * 
 * Create a vertical timeline for showing cultivation process, company history, or product journey.
 * Features alternating layout, scroll animations, and themed styling.
 * 
 * @example
 * ```tsx
 * <VerticalTimeline
 *   items={timelineItems}
 *   theme="cultivation"
 *   alternating={true}
 *   showLine={true}
 *   animateOnScroll={true}
 * />
 * ```
 */ const VerticalTimeline = ({ items, theme = 'cultivation', alternating = true, showLine = true, animateOnScroll = true, className = '', style })=>{
    // Get theme colors
    const getThemeColors = ()=>{
        switch(theme){
            case 'cultivation':
                return {
                    primary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    secondary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[400],
                    accent: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].gold[400]
                };
            case 'company':
                return {
                    primary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].charcoal[700],
                    secondary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[300],
                    accent: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].apothecary
                };
            case 'product':
                return {
                    primary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].gold[600],
                    secondary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[300],
                    accent: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[500]
                };
            default:
                return {
                    primary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    secondary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[400],
                    accent: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].gold[400]
                };
        }
    };
    const themeColors = getThemeColors();
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.3,
                delayChildren: 0.2
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `vertical-timeline relative ${className}`,
        style: style,
        children: [
            showLine && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute left-1/2 transform -translate-x-1/2 w-1 h-full",
                style: {
                    backgroundColor: themeColors.secondary
                }
            }, void 0, false, {
                fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                lineNumber: 83,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: containerVariants,
                initial: "hidden",
                whileInView: "visible",
                viewport: {
                    once: true,
                    margin: '-100px'
                },
                className: "space-y-12",
                children: items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TimelineItemComponent, {
                        item: item,
                        index: index,
                        isAlternating: alternating,
                        themeColors: themeColors,
                        animateOnScroll: animateOnScroll
                    }, item.id, false, {
                        fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                        lineNumber: 97,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                lineNumber: 89,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
};
_c = VerticalTimeline;
const TimelineItemComponent = ({ item, index, isAlternating, themeColors, animateOnScroll })=>{
    _s();
    const [ref, inView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"])({
        threshold: 0.3,
        triggerOnce: animateOnScroll
    });
    const isLeft = isAlternating ? index % 2 === 0 : false;
    const itemVariants = {
        hidden: {
            opacity: 0,
            x: isLeft ? -50 : 50,
            y: 30
        },
        visible: {
            opacity: 1,
            x: 0,
            y: 0,
            transition: {
                duration: 0.6,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        }
    };
    const iconVariants = {
        hidden: {
            scale: 0,
            rotate: -180
        },
        visible: {
            scale: 1,
            rotate: 0,
            transition: {
                duration: 0.5,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.bounce,
                delay: 0.2
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        ref: ref,
        variants: itemVariants,
        initial: "hidden",
        animate: animateOnScroll ? inView ? 'visible' : 'hidden' : 'visible',
        className: `relative flex items-center ${isAlternating ? isLeft ? 'flex-row-reverse' : 'flex-row' : 'flex-row'}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `w-full ${isAlternating ? 'md:w-5/12' : 'md:w-10/12 md:ml-16'} ${isLeft ? 'md:pr-8' : 'md:pl-8'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "bg-cream-50 rounded-xl p-6 relative",
                    style: {
                        boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].soft
                    },
                    whileHover: {
                        y: -4
                    },
                    transition: {
                        duration: 0.2
                    },
                    children: [
                        isAlternating && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `absolute top-6 w-0 h-0 ${isLeft ? 'right-0 border-l-8 border-l-cream-50 border-y-8 border-y-transparent' : 'left-0 border-r-8 border-r-cream-50 border-y-8 border-y-transparent'}`
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                            lineNumber: 196,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-block px-3 py-1 rounded-full text-sm font-medium text-cream-50",
                                style: {
                                    backgroundColor: themeColors.accent
                                },
                                children: item.date
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                                lineNumber: 207,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                            lineNumber: 206,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-xl font-bold text-charcoal-800 mb-3",
                            children: item.title
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                            lineNumber: 216,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 leading-relaxed mb-4",
                            children: item.description
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, this),
                        item.image && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative h-48 rounded-lg overflow-hidden mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                src: item.image,
                                alt: item.title,
                                fill: true,
                                className: "object-cover"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                                lineNumber: 228,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                            lineNumber: 227,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                    lineNumber: 188,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                lineNumber: 183,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: iconVariants,
                initial: "hidden",
                animate: animateOnScroll ? inView ? 'visible' : 'hidden' : 'visible',
                className: `absolute ${isAlternating ? 'left-1/2 transform -translate-x-1/2' : 'left-6'} z-10`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-12 h-12 rounded-full flex items-center justify-center border-4 border-cream-50",
                    style: {
                        backgroundColor: themeColors.primary
                    },
                    children: item.icon ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-cream-50",
                        children: item.icon
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                        lineNumber: 253,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-3 h-3 bg-cream-50 rounded-full"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                        lineNumber: 257,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                    lineNumber: 248,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                lineNumber: 240,
                columnNumber: 7
            }, this),
            isAlternating && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-5/12 hidden md:block"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
                lineNumber: 264,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/layout/VerticalTimeline.tsx",
        lineNumber: 169,
        columnNumber: 5
    }, this);
};
_s(TimelineItemComponent, "GpcLnEGLCRT/LcXgsVwPMCbjDPg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"]
    ];
});
_c1 = TimelineItemComponent;
const __TURBOPACK__default__export__ = VerticalTimeline;
var _c, _c1;
__turbopack_context__.k.register(_c, "VerticalTimeline");
__turbopack_context__.k.register(_c1, "TimelineItemComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/layout/SplitMediaLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-intersection-observer/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
/**
 * SplitMediaLayout Component
 * 
 * Build split-screen layouts combining media and content for product education.
 * Features responsive design, animation triggers, and flexible media/content positioning.
 * 
 * @example
 * ```tsx
 * <SplitMediaLayout
 *   media={{
 *     type: 'image',
 *     src: '/images/extraction-process.jpg',
 *     alt: 'Cannabis extraction process'
 *   }}
 *   content={{
 *     title: 'Our Extraction Process',
 *     description: 'We use state-of-the-art CO2 extraction methods to preserve the full spectrum of cannabinoids and terpenes.',
 *     features: ['CO2 Extraction', 'Full Spectrum', 'Lab Tested', 'Solvent-Free'],
 *     cta: {
 *       text: 'Learn More',
 *       action: () => router.push('/process')
 *     }
 *   }}
 *   mediaPosition="left"
 *   splitRatio="50-50"
 *   verticalAlign="center"
 * />
 * ```
 */ const SplitMediaLayout = ({ media, content, mediaPosition = 'left', splitRatio = '50-50', verticalAlign = 'center', className = '', style })=>{
    _s();
    const [ref, inView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"])({
        threshold: 0.3,
        triggerOnce: true
    });
    // Get split ratio classes
    const getSplitRatioClasses = ()=>{
        switch(splitRatio){
            case '60-40':
                return mediaPosition === 'left' ? {
                    media: 'lg:w-3/5',
                    content: 'lg:w-2/5'
                } : {
                    media: 'lg:w-2/5',
                    content: 'lg:w-3/5'
                };
            case '40-60':
                return mediaPosition === 'left' ? {
                    media: 'lg:w-2/5',
                    content: 'lg:w-3/5'
                } : {
                    media: 'lg:w-3/5',
                    content: 'lg:w-2/5'
                };
            case '50-50':
            default:
                return {
                    media: 'lg:w-1/2',
                    content: 'lg:w-1/2'
                };
        }
    };
    const ratioClasses = getSplitRatioClasses();
    // Get vertical alignment classes
    const getVerticalAlignClasses = ()=>{
        switch(verticalAlign){
            case 'top':
                return 'items-start';
            case 'bottom':
                return 'items-end';
            case 'center':
            default:
                return 'items-center';
        }
    };
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.3,
                delayChildren: 0.2
            }
        }
    };
    const mediaVariants = {
        hidden: {
            opacity: 0,
            x: mediaPosition === 'left' ? -50 : 50,
            scale: 0.9
        },
        visible: {
            opacity: 1,
            x: 0,
            scale: 1,
            transition: {
                duration: 0.6,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        }
    };
    const contentVariants = {
        hidden: {
            opacity: 0,
            x: mediaPosition === 'left' ? 50 : -50,
            y: 20
        },
        visible: {
            opacity: 1,
            x: 0,
            y: 0,
            transition: {
                duration: 0.6,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth,
                delay: 0.2
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].section, {
        ref: ref,
        variants: containerVariants,
        initial: "hidden",
        animate: inView ? 'visible' : 'hidden',
        className: `split-media-layout py-16 ${className}`,
        style: style,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `flex flex-col lg:flex-row ${getVerticalAlignClasses()} gap-8 lg:gap-12`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        variants: mediaVariants,
                        className: `${ratioClasses.media} ${mediaPosition === 'right' ? 'lg:order-2' : 'lg:order-1'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                media.type === 'image' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative aspect-[4/3] rounded-xl overflow-hidden",
                                    style: {
                                        boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].large
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: media.src,
                                            alt: media.alt || '',
                                            fill: true,
                                            className: "object-cover",
                                            sizes: "(max-width: 768px) 100vw, 50vw"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                            lineNumber: 155,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                            lineNumber: 164,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 151,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative aspect-video rounded-xl overflow-hidden",
                                    style: {
                                        boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].large
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                        src: media.src,
                                        controls: true,
                                        className: "w-full h-full object-cover",
                                        poster: media.alt,
                                        children: "Your browser does not support the video tag."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                        lineNumber: 171,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 167,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "absolute -top-4 -right-4 w-8 h-8 rounded-full",
                                    style: {
                                        backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].apothecary
                                    },
                                    initial: {
                                        scale: 0,
                                        opacity: 0
                                    },
                                    animate: inView ? {
                                        scale: 1,
                                        opacity: 0.6
                                    } : {
                                        scale: 0,
                                        opacity: 0
                                    },
                                    transition: {
                                        delay: 0.8,
                                        duration: 0.4
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 183,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "absolute -bottom-4 -left-4 w-6 h-6 rounded-full",
                                    style: {
                                        backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].gold[400]
                                    },
                                    initial: {
                                        scale: 0,
                                        opacity: 0
                                    },
                                    animate: inView ? {
                                        scale: 1,
                                        opacity: 0.4
                                    } : {
                                        scale: 0,
                                        opacity: 0
                                    },
                                    transition: {
                                        delay: 1,
                                        duration: 0.4
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 190,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                            lineNumber: 149,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                        lineNumber: 143,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        variants: contentVariants,
                        className: `${ratioClasses.content} ${mediaPosition === 'right' ? 'lg:order-1' : 'lg:order-2'} flex flex-col justify-center`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].h2, {
                                    className: "text-3xl lg:text-4xl font-serif font-bold text-charcoal-800 leading-tight",
                                    initial: {
                                        opacity: 0,
                                        y: 20
                                    },
                                    animate: inView ? {
                                        opacity: 1,
                                        y: 0
                                    } : {
                                        opacity: 0,
                                        y: 20
                                    },
                                    transition: {
                                        delay: 0.4,
                                        duration: 0.5
                                    },
                                    children: content.title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 209,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].p, {
                                    className: "text-lg text-charcoal-600 leading-relaxed",
                                    initial: {
                                        opacity: 0,
                                        y: 20
                                    },
                                    animate: inView ? {
                                        opacity: 1,
                                        y: 0
                                    } : {
                                        opacity: 0,
                                        y: 20
                                    },
                                    transition: {
                                        delay: 0.5,
                                        duration: 0.5
                                    },
                                    children: content.description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 219,
                                    columnNumber: 15
                                }, this),
                                content.features && content.features.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "space-y-3",
                                    initial: {
                                        opacity: 0,
                                        y: 20
                                    },
                                    animate: inView ? {
                                        opacity: 1,
                                        y: 0
                                    } : {
                                        opacity: 0,
                                        y: 20
                                    },
                                    transition: {
                                        delay: 0.6,
                                        duration: 0.5
                                    },
                                    children: content.features.map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                            className: "flex items-center space-x-3",
                                            initial: {
                                                opacity: 0,
                                                x: -20
                                            },
                                            animate: inView ? {
                                                opacity: 1,
                                                x: 0
                                            } : {
                                                opacity: 0,
                                                x: -20
                                            },
                                            transition: {
                                                delay: 0.7 + index * 0.1,
                                                duration: 0.4
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-2 h-2 rounded-full flex-shrink-0",
                                                    style: {
                                                        backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].apothecary
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                                    lineNumber: 244,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-charcoal-700 font-medium",
                                                    children: feature
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                                    lineNumber: 248,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                            lineNumber: 237,
                                            columnNumber: 21
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 230,
                                    columnNumber: 17
                                }, this),
                                content.cta && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    initial: {
                                        opacity: 0,
                                        y: 20
                                    },
                                    animate: inView ? {
                                        opacity: 1,
                                        y: 0
                                    } : {
                                        opacity: 0,
                                        y: 20
                                    },
                                    transition: {
                                        delay: 0.8,
                                        duration: 0.5
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                                        onClick: content.cta.action,
                                        whileHover: {
                                            scale: 1.05
                                        },
                                        whileTap: {
                                            scale: 0.95
                                        },
                                        className: "inline-flex items-center px-6 py-3 rounded-lg font-semibold text-cream-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2",
                                        style: {
                                            backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                                            focusRingColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[400]
                                        },
                                        onMouseEnter: (e)=>{
                                            e.currentTarget.style.backgroundColor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[700];
                                        },
                                        onMouseLeave: (e)=>{
                                            e.currentTarget.style.backgroundColor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600];
                                        },
                                        children: [
                                            content.cta.text,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                className: "ml-2 h-5 w-5",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M9 5l7 7-7 7"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                                    lineNumber: 281,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                                lineNumber: 280,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                        lineNumber: 263,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                                    lineNumber: 258,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                            lineNumber: 207,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                        lineNumber: 201,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
                lineNumber: 141,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
            lineNumber: 140,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/layout/SplitMediaLayout.tsx",
        lineNumber: 132,
        columnNumber: 5
    }, this);
};
_s(SplitMediaLayout, "GpcLnEGLCRT/LcXgsVwPMCbjDPg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"]
    ];
});
_c = SplitMediaLayout;
const __TURBOPACK__default__export__ = SplitMediaLayout;
var _c;
__turbopack_context__.k.register(_c, "SplitMediaLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-intersection-observer/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
/**
 * FeatureIconsWithMotion Component
 * 
 * Build feature highlight sections with animated icons using Framer Motion.
 * Features customizable layouts, animation triggers, and cannabis industry icons.
 * 
 * @example
 * ```tsx
 * <FeatureIconsWithMotion
 *   features={features}
 *   layout="grid"
 *   animationTrigger="scroll"
 *   columns={3}
 *   iconSize="lg"
 * />
 * ```
 */ const FeatureIconsWithMotion = ({ features, layout = 'grid', animationTrigger = 'scroll', columns = 3, iconSize = 'md', className = '', style })=>{
    _s();
    const [ref, inView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"])({
        threshold: 0.2,
        triggerOnce: animationTrigger === 'scroll'
    });
    // Get icon size classes
    const getIconSizeClasses = ()=>{
        switch(iconSize){
            case 'sm':
                return {
                    container: 'w-12 h-12',
                    icon: 'w-6 h-6'
                };
            case 'md':
                return {
                    container: 'w-16 h-16',
                    icon: 'w-8 h-8'
                };
            case 'lg':
                return {
                    container: 'w-20 h-20',
                    icon: 'w-10 h-10'
                };
            case 'xl':
                return {
                    container: 'w-24 h-24',
                    icon: 'w-12 h-12'
                };
            default:
                return {
                    container: 'w-16 h-16',
                    icon: 'w-8 h-8'
                };
        }
    };
    const iconSizeClasses = getIconSizeClasses();
    // Get layout classes
    const getLayoutClasses = ()=>{
        switch(layout){
            case 'horizontal':
                return 'flex flex-wrap justify-center gap-8';
            case 'vertical':
                return 'space-y-8';
            case 'grid':
            default:
                return `grid gap-8 ${columns === 1 ? 'grid-cols-1' : columns === 2 ? 'grid-cols-1 md:grid-cols-2' : columns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : columns === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'}`;
        }
    };
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2,
                delayChildren: 0.1
            }
        }
    };
    const itemVariants = {
        hidden: {
            opacity: 0,
            y: 30,
            scale: 0.8
        },
        visible: {
            opacity: 1,
            y: 0,
            scale: 1,
            transition: {
                duration: 0.5,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        }
    };
    const iconVariants = {
        hidden: {
            scale: 0,
            rotate: -180
        },
        visible: {
            scale: 1,
            rotate: 0,
            transition: {
                duration: 0.6,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.bounce,
                delay: 0.2
            }
        },
        hover: {
            scale: 1.1,
            rotate: 5,
            transition: {
                duration: 0.2,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        ref: ref,
        variants: containerVariants,
        initial: "hidden",
        animate: animationTrigger === 'scroll' ? inView ? 'visible' : 'hidden' : animationTrigger === 'load' ? 'visible' : 'hidden',
        className: `feature-icons-with-motion ${className}`,
        style: style,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: getLayoutClasses(),
            children: features.map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FeatureCard, {
                    feature: feature,
                    index: index,
                    iconSizeClasses: iconSizeClasses,
                    itemVariants: itemVariants,
                    iconVariants: iconVariants,
                    animationTrigger: animationTrigger,
                    layout: layout
                }, feature.id, false, {
                    fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                    lineNumber: 146,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
            lineNumber: 144,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
        lineNumber: 130,
        columnNumber: 5
    }, this);
};
_s(FeatureIconsWithMotion, "GpcLnEGLCRT/LcXgsVwPMCbjDPg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"]
    ];
});
_c = FeatureIconsWithMotion;
const FeatureCard = ({ feature, index, iconSizeClasses, itemVariants, iconVariants, animationTrigger, layout })=>{
    _s1();
    const [cardRef, cardInView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"])({
        threshold: 0.3,
        triggerOnce: true
    });
    const getFeatureColor = ()=>{
        if (feature.color) return feature.color;
        // Default colors based on index
        const colors = [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[500],
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].apothecary,
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].gold[500],
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[500],
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].charcoal[600]
        ];
        return colors[index % colors.length];
    };
    const featureColor = getFeatureColor();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        ref: cardRef,
        variants: itemVariants,
        className: `feature-card text-center ${layout === 'horizontal' ? 'flex-shrink-0' : ''}`,
        whileHover: animationTrigger === 'hover' ? {
            y: -8
        } : undefined,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: iconVariants,
                whileHover: "hover",
                className: `${iconSizeClasses.container} mx-auto mb-4 rounded-xl flex items-center justify-center relative overflow-hidden`,
                style: {
                    backgroundColor: `${featureColor}15`,
                    boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].soft
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 rounded-xl",
                        style: {
                            backgroundColor: featureColor
                        },
                        initial: {
                            opacity: 0.1
                        },
                        whileHover: {
                            opacity: 0.2
                        },
                        transition: {
                            duration: 0.2
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                        lineNumber: 223,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: `${iconSizeClasses.icon} relative z-10`,
                        style: {
                            color: featureColor
                        },
                        whileHover: {
                            filter: 'brightness(1.2)',
                            transition: {
                                duration: 0.2
                            }
                        },
                        children: feature.icon
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                        lineNumber: 232,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 rounded-xl border-2",
                        style: {
                            borderColor: featureColor
                        },
                        initial: {
                            scale: 1,
                            opacity: 0
                        },
                        animate: animationTrigger === 'scroll' && cardInView ? {
                            scale: [
                                1,
                                1.2,
                                1
                            ],
                            opacity: [
                                0,
                                0.6,
                                0
                            ]
                        } : {},
                        transition: {
                            duration: 2,
                            repeat: Infinity,
                            delay: index * 0.2
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                        lineNumber: 244,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                lineNumber: 213,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 20
                },
                animate: animationTrigger === 'scroll' && cardInView ? {
                    opacity: 1,
                    y: 0
                } : animationTrigger === 'load' ? {
                    opacity: 1,
                    y: 0
                } : {
                    opacity: 0,
                    y: 20
                },
                transition: {
                    duration: 0.5,
                    delay: 0.3 + index * 0.1,
                    ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold mb-2",
                        style: {
                            color: featureColor
                        },
                        children: feature.title
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                        lineNumber: 280,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-charcoal-600 leading-relaxed",
                        children: feature.description
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                        lineNumber: 286,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                lineNumber: 265,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute -top-2 -right-2 w-4 h-4 rounded-full",
                style: {
                    backgroundColor: featureColor
                },
                initial: {
                    scale: 0,
                    opacity: 0
                },
                animate: animationTrigger === 'scroll' && cardInView ? {
                    scale: 1,
                    opacity: 0.3
                } : animationTrigger === 'load' ? {
                    scale: 1,
                    opacity: 0.3
                } : {
                    scale: 0,
                    opacity: 0
                },
                transition: {
                    duration: 0.4,
                    delay: 0.6 + index * 0.1,
                    ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.bounce
                }
            }, void 0, false, {
                fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
                lineNumber: 292,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx",
        lineNumber: 204,
        columnNumber: 5
    }, this);
};
_s1(FeatureCard, "Q4q3eFVcz7/p0N7VCm8XFgbGb0w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInView"]
    ];
});
_c1 = FeatureCard;
const __TURBOPACK__default__export__ = FeatureIconsWithMotion;
var _c, _c1;
__turbopack_context__.k.register(_c, "FeatureIconsWithMotion");
__turbopack_context__.k.register(_c1, "FeatureCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/navigation/FAQAccordion.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
/**
 * FAQAccordion Component
 * 
 * Create an FAQ accordion with smooth animations and cannabis industry specific content.
 * Features search functionality, categories, and smooth expand/collapse animations.
 * 
 * @example
 * ```tsx
 * <FAQAccordion
 *   items={faqItems}
 *   allowMultiple={false}
 *   defaultOpen={['faq-1']}
 *   showCategories={true}
 *   enableSearch={true}
 * />
 * ```
 */ const FAQAccordion = ({ items, allowMultiple = false, defaultOpen = [], showCategories = false, enableSearch = false, className = '', style })=>{
    _s();
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultOpen);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedCategory, setSelectedCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // Get unique categories
    const categories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FAQAccordion.useMemo[categories]": ()=>{
            const cats = Array.from(new Set(items.map({
                "FAQAccordion.useMemo[categories].cats": (item)=>item.category
            }["FAQAccordion.useMemo[categories].cats"]).filter(Boolean)));
            return cats;
        }
    }["FAQAccordion.useMemo[categories]"], [
        items
    ]);
    // Filter items based on search and category
    const filteredItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FAQAccordion.useMemo[filteredItems]": ()=>{
            let filtered = items;
            // Apply search filter
            if (searchTerm) {
                filtered = filtered.filter({
                    "FAQAccordion.useMemo[filteredItems]": (item)=>item.question.toLowerCase().includes(searchTerm.toLowerCase()) || item.answer.toLowerCase().includes(searchTerm.toLowerCase())
                }["FAQAccordion.useMemo[filteredItems]"]);
            }
            // Apply category filter
            if (selectedCategory) {
                filtered = filtered.filter({
                    "FAQAccordion.useMemo[filteredItems]": (item)=>item.category === selectedCategory
                }["FAQAccordion.useMemo[filteredItems]"]);
            }
            return filtered;
        }
    }["FAQAccordion.useMemo[filteredItems]"], [
        items,
        searchTerm,
        selectedCategory
    ]);
    // Toggle item open/closed
    const toggleItem = (itemId)=>{
        setOpenItems((prev)=>{
            if (allowMultiple) {
                return prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [
                    ...prev,
                    itemId
                ];
            } else {
                return prev.includes(itemId) ? [] : [
                    itemId
                ];
            }
        });
    };
    // Clear search and filters
    const clearFilters = ()=>{
        setSearchTerm('');
        setSelectedCategory('');
    };
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2
            }
        }
    };
    const itemVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.4,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `faq-accordion ${className}`,
        style: style,
        children: [
            (enableSearch || showCategories) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8 space-y-4",
                children: [
                    enableSearch && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative max-w-md",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "text",
                                placeholder: "Search FAQs...",
                                value: searchTerm,
                                onChange: (e)=>setSearchTerm(e.target.value),
                                className: "w-full pl-10 pr-4 py-3 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                lineNumber: 114,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "absolute left-3 top-3.5 h-5 w-5 text-gray-400",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                    lineNumber: 122,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                lineNumber: 121,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 113,
                        columnNumber: 13
                    }, this),
                    showCategories && categories.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setSelectedCategory(''),
                                className: `px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 ${selectedCategory === '' ? 'bg-primary-600 text-cream-50 border-primary-600' : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'}`,
                                children: "All Categories"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                lineNumber: 130,
                                columnNumber: 15
                            }, this),
                            categories.map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setSelectedCategory(category),
                                    className: `px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 capitalize ${selectedCategory === category ? 'bg-primary-600 text-cream-50 border-primary-600' : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'}`,
                                    children: category
                                }, category, false, {
                                    fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                    lineNumber: 141,
                                    columnNumber: 17
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 129,
                        columnNumber: 13
                    }, this),
                    (searchTerm || selectedCategory) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: [
                                    searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700",
                                        children: [
                                            'Search: "',
                                            searchTerm,
                                            '"'
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                        lineNumber: 161,
                                        columnNumber: 19
                                    }, this),
                                    selectedCategory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700 capitalize",
                                        children: [
                                            "Category: ",
                                            selectedCategory
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                        lineNumber: 166,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                lineNumber: 159,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: clearFilters,
                                className: "text-sm text-primary-600 hover:text-primary-700 font-medium",
                                children: "Clear Filters"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                lineNumber: 171,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 158,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600",
                        children: [
                            "Showing ",
                            filteredItems.length,
                            " of ",
                            items.length,
                            " questions"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 181,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                lineNumber: 110,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: containerVariants,
                initial: "hidden",
                whileInView: "visible",
                viewport: {
                    once: true,
                    margin: '-50px'
                },
                className: "space-y-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                    children: filteredItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FAQItemComponent, {
                            item: item,
                            isOpen: openItems.includes(item.id),
                            onToggle: ()=>toggleItem(item.id),
                            itemVariants: itemVariants
                        }, item.id, false, {
                            fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                            lineNumber: 197,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                    lineNumber: 195,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                lineNumber: 188,
                columnNumber: 7
            }, this),
            filteredItems.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center py-12",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-8 h-8 text-gray-400",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                lineNumber: 213,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                            lineNumber: 212,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 211,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 mb-2",
                        children: "No questions found"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 216,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-500",
                        children: "Try adjusting your search or filter criteria."
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 217,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                lineNumber: 210,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
        lineNumber: 107,
        columnNumber: 5
    }, this);
};
_s(FAQAccordion, "77ySXXuQSngJo1MBZ+H0k1rLDN8=");
_c = FAQAccordion;
const FAQItemComponent = ({ item, isOpen, onToggle, itemVariants })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: itemVariants,
        layout: true,
        className: "faq-item bg-cream-50 rounded-lg overflow-hidden",
        style: {
            boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandShadows"].soft
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                onClick: onToggle,
                className: "w-full px-6 py-4 text-left flex items-center justify-between hover:bg-cream-100 transition-colors duration-200",
                whileHover: {
                    backgroundColor: 'rgba(0, 0, 0, 0.02)'
                },
                whileTap: {
                    scale: 0.99
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-lg font-semibold text-charcoal-800 pr-4",
                        children: item.question
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 252,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        animate: {
                            rotate: isOpen ? 180 : 0
                        },
                        transition: {
                            duration: 0.2,
                            ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                        },
                        className: "flex-shrink-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-5 h-5 text-primary-600",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M19 9l-7 7-7-7"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                lineNumber: 267,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                            lineNumber: 261,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 256,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                lineNumber: 246,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                children: isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    initial: {
                        height: 0,
                        opacity: 0
                    },
                    animate: {
                        height: 'auto',
                        opacity: 1
                    },
                    exit: {
                        height: 0,
                        opacity: 0
                    },
                    transition: {
                        duration: 0.3,
                        ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                    },
                    className: "overflow-hidden",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "px-6 pb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "border-t border-cream-200 pt-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-charcoal-600 leading-relaxed",
                                    children: item.answer
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                    lineNumber: 292,
                                    columnNumber: 17
                                }, this),
                                item.category && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-3",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "inline-block px-3 py-1 text-xs font-medium bg-primary-100 text-primary-700 rounded-full capitalize",
                                        children: item.category
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                        lineNumber: 299,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                                    lineNumber: 298,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                            lineNumber: 291,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                        lineNumber: 290,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                    lineNumber: 280,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
                lineNumber: 278,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/navigation/FAQAccordion.tsx",
        lineNumber: 239,
        columnNumber: 5
    }, this);
};
_c1 = FAQItemComponent;
const __TURBOPACK__default__export__ = FAQAccordion;
var _c, _c1;
__turbopack_context__.k.register(_c, "FAQAccordion");
__turbopack_context__.k.register(_c1, "FAQItemComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/navigation/AnnouncementBanner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
/**
 * AnnouncementBanner Component
 * 
 * Build announcement banners for deals, new products, and compliance notices.
 * Features auto-dismiss, sticky positioning, and themed styling for different message types.
 * 
 * @example
 * ```tsx
 * <AnnouncementBanner
 *   message="🌿 New Premium Rosin Collection Available - 20% Off This Week!"
 *   type="deal"
 *   dismissible={true}
 *   autoDismiss={10000}
 *   actionText="Shop Now"
 *   onAction={() => router.push('/products/rosin')}
 *   sticky={true}
 * />
 * ```
 */ const AnnouncementBanner = ({ message, type = 'info', dismissible = true, autoDismiss, actionText, onAction, onClose, sticky = false, className = '', style })=>{
    _s();
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [timeLeft, setTimeLeft] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Auto-dismiss functionality
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnnouncementBanner.useEffect": ()=>{
            if (autoDismiss && autoDismiss > 0) {
                setTimeLeft(autoDismiss);
                const interval = setInterval({
                    "AnnouncementBanner.useEffect.interval": ()=>{
                        setTimeLeft({
                            "AnnouncementBanner.useEffect.interval": (prev)=>{
                                if (prev && prev <= 1000) {
                                    handleClose();
                                    return null;
                                }
                                return prev ? prev - 1000 : null;
                            }
                        }["AnnouncementBanner.useEffect.interval"]);
                    }
                }["AnnouncementBanner.useEffect.interval"], 1000);
                return ({
                    "AnnouncementBanner.useEffect": ()=>clearInterval(interval)
                })["AnnouncementBanner.useEffect"];
            }
        }
    }["AnnouncementBanner.useEffect"], [
        autoDismiss
    ]);
    // Handle close
    const handleClose = ()=>{
        setIsVisible(false);
        onClose?.();
    };
    // Get banner styling based on type
    const getBannerStyling = ()=>{
        switch(type){
            case 'info':
                return {
                    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    textColor: 'text-cream-50',
                    iconColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[100],
                    actionBg: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[50],
                    actionText: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    actionHover: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[100]
                };
            case 'warning':
                return {
                    backgroundColor: '#f59e0b',
                    textColor: 'text-cream-50',
                    iconColor: '#fef3c7',
                    actionBg: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[50],
                    actionText: '#f59e0b',
                    actionHover: '#fef3c7'
                };
            case 'success':
                return {
                    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[600],
                    textColor: 'text-cream-50',
                    iconColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[100],
                    actionBg: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[50],
                    actionText: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[600],
                    actionHover: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].sage[100]
                };
            case 'error':
                return {
                    backgroundColor: '#dc2626',
                    textColor: 'text-cream-50',
                    iconColor: '#fecaca',
                    actionBg: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[50],
                    actionText: '#dc2626',
                    actionHover: '#fecaca'
                };
            case 'deal':
                return {
                    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].gold[600],
                    textColor: 'text-charcoal-800',
                    iconColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].gold[200],
                    actionBg: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].charcoal[800],
                    actionText: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[50],
                    actionHover: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].charcoal[700]
                };
            default:
                return {
                    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    textColor: 'text-cream-50',
                    iconColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[100],
                    actionBg: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[50],
                    actionText: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    actionHover: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].cream[100]
                };
        }
    };
    const styling = getBannerStyling();
    // Get icon based on type
    const getIcon = ()=>{
        switch(type){
            case 'info':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-5 h-5",
                    fill: "currentColor",
                    viewBox: "0 0 20 20",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        fillRule: "evenodd",
                        d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",
                        clipRule: "evenodd"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                        lineNumber: 135,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                    lineNumber: 134,
                    columnNumber: 11
                }, this);
            case 'warning':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-5 h-5",
                    fill: "currentColor",
                    viewBox: "0 0 20 20",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        fillRule: "evenodd",
                        d: "M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",
                        clipRule: "evenodd"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                        lineNumber: 141,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                    lineNumber: 140,
                    columnNumber: 11
                }, this);
            case 'success':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-5 h-5",
                    fill: "currentColor",
                    viewBox: "0 0 20 20",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        fillRule: "evenodd",
                        d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                        clipRule: "evenodd"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                        lineNumber: 147,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                    lineNumber: 146,
                    columnNumber: 11
                }, this);
            case 'error':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-5 h-5",
                    fill: "currentColor",
                    viewBox: "0 0 20 20",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        fillRule: "evenodd",
                        d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",
                        clipRule: "evenodd"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                        lineNumber: 153,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                    lineNumber: 152,
                    columnNumber: 11
                }, this);
            case 'deal':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-5 h-5",
                    fill: "currentColor",
                    viewBox: "0 0 20 20",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        fillRule: "evenodd",
                        d: "M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z",
                        clipRule: "evenodd"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                        lineNumber: 159,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                    lineNumber: 158,
                    columnNumber: 11
                }, this);
            default:
                return null;
        }
    };
    // Format time left for display
    const formatTimeLeft = (ms)=>{
        const seconds = Math.ceil(ms / 1000);
        return seconds;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
        children: isVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                y: -100,
                opacity: 0
            },
            animate: {
                y: 0,
                opacity: 1
            },
            exit: {
                y: -100,
                opacity: 0
            },
            transition: {
                duration: 0.4,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            },
            className: `announcement-banner ${sticky ? 'fixed top-0 left-0 right-0 z-50' : 'relative'} ${className}`,
            style: {
                backgroundColor: styling.backgroundColor,
                ...style
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between py-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3 flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            color: styling.iconColor
                                        },
                                        children: getIcon()
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                        lineNumber: 197,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `font-medium ${styling.textColor} flex-1`,
                                        children: message
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                        lineNumber: 202,
                                        columnNumber: 17
                                    }, this),
                                    timeLeft && timeLeft > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `text-sm ${styling.textColor} opacity-75`,
                                        children: [
                                            "Auto-close in ",
                                            formatTimeLeft(timeLeft),
                                            "s"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                        lineNumber: 208,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                lineNumber: 195,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    actionText && onAction && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                                        onClick: onAction,
                                        whileHover: {
                                            scale: 1.05
                                        },
                                        whileTap: {
                                            scale: 0.95
                                        },
                                        className: "px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                                        style: {
                                            backgroundColor: styling.actionBg,
                                            color: styling.actionText
                                        },
                                        onMouseEnter: (e)=>{
                                            e.currentTarget.style.backgroundColor = styling.actionHover;
                                        },
                                        onMouseLeave: (e)=>{
                                            e.currentTarget.style.backgroundColor = styling.actionBg;
                                        },
                                        children: actionText
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                        lineNumber: 218,
                                        columnNumber: 19
                                    }, this),
                                    dismissible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                                        onClick: handleClose,
                                        whileHover: {
                                            scale: 1.1
                                        },
                                        whileTap: {
                                            scale: 0.9
                                        },
                                        className: `p-1 rounded-md transition-colors duration-200 ${styling.textColor} hover:bg-black hover:bg-opacity-10`,
                                        "aria-label": "Close announcement",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                            className: "w-4 h-4",
                                            fill: "currentColor",
                                            viewBox: "0 0 20 20",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                fillRule: "evenodd",
                                                d: "M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",
                                                clipRule: "evenodd"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                                lineNumber: 248,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                            lineNumber: 247,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                        lineNumber: 240,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                                lineNumber: 215,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                        lineNumber: 193,
                        columnNumber: 13
                    }, this),
                    autoDismiss && timeLeft && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "h-1 bg-black bg-opacity-20",
                        initial: {
                            scaleX: 1
                        },
                        animate: {
                            scaleX: 0
                        },
                        transition: {
                            duration: autoDismiss / 1000,
                            ease: 'linear'
                        },
                        style: {
                            transformOrigin: 'left'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                        lineNumber: 257,
                        columnNumber: 15
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
                lineNumber: 192,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
            lineNumber: 176,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/navigation/AnnouncementBanner.tsx",
        lineNumber: 174,
        columnNumber: 5
    }, this);
};
_s(AnnouncementBanner, "jGKRlyRcsORJYWiGJbMuxGXfwm0=");
_c = AnnouncementBanner;
const __TURBOPACK__default__export__ = AnnouncementBanner;
var _c;
__turbopack_context__.k.register(_c, "AnnouncementBanner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/demos/UIComponentsShowcase.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$hero$2f$HeroVideoWithCTA$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/hero/HeroVideoWithCTA.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$cards$2f$AnimatedCardSlider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/cards/AnimatedCardSlider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$grids$2f$TabbedProductGrid$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/grids/TabbedProductGrid.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$grids$2f$MasonryProductShowcase$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/grids/MasonryProductShowcase.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$effects$2f$GlitchTextEffect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/effects/GlitchTextEffect.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$cards$2f$FilterableStrainCards$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/cards/FilterableStrainCards.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$layout$2f$VerticalTimeline$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/layout/VerticalTimeline.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$layout$2f$SplitMediaLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/layout/SplitMediaLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$effects$2f$FeatureIconsWithMotion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/effects/FeatureIconsWithMotion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$navigation$2f$FAQAccordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/navigation/FAQAccordion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$navigation$2f$AnnouncementBanner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/navigation/AnnouncementBanner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
/**
 * UI Components Showcase
 * 
 * Comprehensive demo page showcasing all Apothecary Farms UI components
 */ const UIComponentsShowcase = ()=>{
    // Sample data for demos
    const sampleProducts = [
        {
            id: '1',
            name: 'Blue Dream',
            description: 'A balanced hybrid strain with sweet berry aroma and cerebral effects.',
            image: '/api/placeholder/300/400',
            price: 45,
            category: 'flower',
            strain: 'Hybrid',
            thc: 18,
            cbd: 1,
            effects: [
                'Relaxed',
                'Happy',
                'Creative'
            ],
            tags: [
                'Popular',
                'Daytime'
            ]
        },
        {
            id: '2',
            name: 'Live Rosin',
            description: 'Premium solventless concentrate with full terpene profile.',
            image: '/api/placeholder/300/300',
            price: 80,
            category: 'extract',
            thc: 75,
            cbd: 2,
            effects: [
                'Euphoric',
                'Focused',
                'Uplifted'
            ],
            tags: [
                'Premium',
                'Solventless'
            ]
        },
        {
            id: '3',
            name: 'Gummy Bears',
            description: 'Delicious fruit-flavored gummies with precise dosing.',
            image: '/api/placeholder/300/250',
            price: 25,
            category: 'edible',
            thc: 10,
            effects: [
                'Relaxed',
                'Sleepy',
                'Pain Relief'
            ],
            tags: [
                'Beginner Friendly',
                'Tasty'
            ]
        },
        {
            id: '4',
            name: 'OG Kush',
            description: 'Classic indica-dominant strain with earthy pine flavors.',
            image: '/api/placeholder/300/350',
            price: 50,
            category: 'flower',
            strain: 'Indica',
            thc: 22,
            cbd: 0.5,
            effects: [
                'Relaxed',
                'Sleepy',
                'Hungry'
            ],
            tags: [
                'Classic',
                'Evening'
            ]
        },
        {
            id: '5',
            name: 'Shatter',
            description: 'Glass-like concentrate with high potency and purity.',
            image: '/api/placeholder/300/280',
            price: 60,
            category: 'extract',
            thc: 85,
            effects: [
                'Intense',
                'Long-lasting',
                'Cerebral'
            ],
            tags: [
                'High Potency',
                'Experienced'
            ]
        },
        {
            id: '6',
            name: 'Chocolate Bar',
            description: 'Rich dark chocolate infused with premium cannabis.',
            image: '/api/placeholder/300/200',
            price: 35,
            category: 'edible',
            thc: 100,
            effects: [
                'Euphoric',
                'Relaxed',
                'Creative'
            ],
            tags: [
                'Luxury',
                'Microdose'
            ]
        }
    ];
    const categories = [
        {
            id: 'flower',
            name: 'Flower',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "w-4 h-4",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M10 2a6 6 0 00-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 00.515 1.07 32.91 32.91 0 003.256.508 3.5 3.5 0 006.972 0 32.91 32.91 0 003.256-.508.75.75 0 00.515-1.07A11.717 11.717 0 0116 8a6 6 0 00-6-6z"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 107,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 106,
                columnNumber: 9
            }, this)
        },
        {
            id: 'extract',
            name: 'Extracts',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "w-4 h-4",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M6.5 3c-1.051 0-2.093.04-3.125.117a1 1 0 00-.831.814A6.5 6.5 0 004.5 12H13l2.856-2.856A3.5 3.5 0 0015.5 7a3.5 3.5 0 00-3.5-3.5H6.5z"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 116,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 115,
                columnNumber: 9
            }, this)
        },
        {
            id: 'edible',
            name: 'Edibles',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "w-4 h-4",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M10 2a8 8 0 100 16 8 8 0 000-16zM8 12a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 125,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 124,
                columnNumber: 9
            }, this)
        }
    ];
    const filters = [
        {
            id: 'category',
            name: 'Flower',
            value: 'flower'
        },
        {
            id: 'category',
            name: 'Extracts',
            value: 'extract'
        },
        {
            id: 'category',
            name: 'Edibles',
            value: 'edible'
        },
        {
            id: 'price',
            name: 'Under $25',
            value: 'under-25'
        },
        {
            id: 'price',
            name: '$25-$50',
            value: '25-50'
        },
        {
            id: 'price',
            name: '$50-$100',
            value: '50-100'
        },
        {
            id: 'effects',
            name: 'Relaxed',
            value: 'Relaxed'
        },
        {
            id: 'effects',
            name: 'Happy',
            value: 'Happy'
        },
        {
            id: 'effects',
            name: 'Creative',
            value: 'Creative'
        }
    ];
    // Sample strains data
    const sampleStrains = [
        {
            id: '1',
            name: 'Blue Dream',
            type: 'hybrid',
            description: 'A balanced hybrid strain with sweet berry aroma and cerebral effects.',
            image: '/api/placeholder/300/400',
            thc: 18,
            cbd: 1,
            effects: [
                'Relaxed',
                'Happy',
                'Creative'
            ],
            flavors: [
                'Berry',
                'Sweet',
                'Earthy'
            ],
            genetics: 'Blueberry x Haze'
        },
        {
            id: '2',
            name: 'OG Kush',
            type: 'indica',
            description: 'Classic indica-dominant strain with earthy pine flavors.',
            image: '/api/placeholder/300/350',
            thc: 22,
            cbd: 0.5,
            effects: [
                'Relaxed',
                'Sleepy',
                'Hungry'
            ],
            flavors: [
                'Pine',
                'Earthy',
                'Woody'
            ],
            genetics: 'Chemdawg x Lemon Thai'
        },
        {
            id: '3',
            name: 'Sour Diesel',
            type: 'sativa',
            description: 'Energizing sativa with diesel-like aroma and uplifting effects.',
            image: '/api/placeholder/300/380',
            thc: 20,
            cbd: 1,
            effects: [
                'Energetic',
                'Creative',
                'Uplifted'
            ],
            flavors: [
                'Diesel',
                'Citrus',
                'Pungent'
            ],
            genetics: 'Chemdawg 91 x Super Skunk'
        }
    ];
    // Sample timeline data
    const timelineItems = [
        {
            id: '1',
            title: 'Seed Selection',
            description: 'We carefully select premium genetics from trusted breeders worldwide.',
            date: 'Week 1',
            image: '/api/placeholder/400/300'
        },
        {
            id: '2',
            title: 'Germination',
            description: 'Seeds are germinated in controlled environment with optimal humidity and temperature.',
            date: 'Week 2',
            image: '/api/placeholder/400/300'
        },
        {
            id: '3',
            title: 'Vegetative Growth',
            description: 'Plants develop strong root systems and healthy foliage under specialized lighting.',
            date: 'Week 3-8',
            image: '/api/placeholder/400/300'
        },
        {
            id: '4',
            title: 'Flowering',
            description: 'Controlled light cycles trigger flowering, developing potent buds rich in cannabinoids.',
            date: 'Week 9-16',
            image: '/api/placeholder/400/300'
        },
        {
            id: '5',
            title: 'Harvest & Cure',
            description: 'Careful harvesting and curing process preserves terpenes and maximizes potency.',
            date: 'Week 17-20',
            image: '/api/placeholder/400/300'
        }
    ];
    // Sample features data
    const features = [
        {
            id: '1',
            title: 'Lab Tested',
            description: 'All products undergo rigorous third-party testing for potency and purity.',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                fill: "currentColor",
                viewBox: "0 0 20 20",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                        lineNumber: 230,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        fillRule: "evenodd",
                        d: "M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6.5A1.5 1.5 0 009.5 13h-3A1.5 1.5 0 005 11.5V5zM7 7a1 1 0 011-1h.01a1 1 0 110 2H8a1 1 0 01-1-1zm1 3a1 1 0 100 2h.01a1 1 0 100-2H8z",
                        clipRule: "evenodd"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                        lineNumber: 231,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 229,
                columnNumber: 9
            }, this)
        },
        {
            id: '2',
            title: 'Organic Growing',
            description: 'Cultivated using sustainable, organic practices without harmful pesticides.',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                fill: "currentColor",
                viewBox: "0 0 20 20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    fillRule: "evenodd",
                    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",
                    clipRule: "evenodd"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 241,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 240,
                columnNumber: 9
            }, this)
        },
        {
            id: '3',
            title: 'Expert Cultivation',
            description: 'Grown by master cultivators with decades of cannabis growing experience.',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                fill: "currentColor",
                viewBox: "0 0 20 20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 251,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 250,
                columnNumber: 9
            }, this)
        }
    ];
    // Sample FAQ data
    const faqItems = [
        {
            id: '1',
            question: 'What is the difference between indica and sativa?',
            answer: 'Indica strains typically provide relaxing, sedating effects and are often used for evening consumption. Sativa strains tend to be more energizing and uplifting, making them popular for daytime use. Hybrid strains combine characteristics of both.',
            category: 'strains'
        },
        {
            id: '2',
            question: 'How should I store my cannabis products?',
            answer: 'Store cannabis in a cool, dark, dry place away from direct sunlight. Use airtight containers to preserve freshness and potency. Avoid storing in the refrigerator or freezer as this can damage trichomes.',
            category: 'storage'
        },
        {
            id: '3',
            question: 'What does lab testing include?',
            answer: 'Our comprehensive lab testing includes potency analysis (THC, CBD, other cannabinoids), terpene profiles, pesticide screening, heavy metals testing, and microbial analysis to ensure product safety and quality.',
            category: 'testing'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "ui-components-showcase",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-charcoal-800 text-cream-50 py-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6 text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$effects$2f$GlitchTextEffect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            text: "APOTHECARY FARMS",
                            intensity: "medium",
                            trigger: "continuous",
                            fontSize: "4rem",
                            className: "mb-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 284,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-serif mb-4",
                            children: "UI Components Library"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 291,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xl text-cream-200 max-w-3xl mx-auto",
                            children: "Production-ready React components for cannabis industry applications featuring modern animations, responsive design, and brand consistency."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 292,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 283,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 282,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-6xl mx-auto px-6 mb-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                                children: "Hero Video with CTA"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                lineNumber: 302,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-charcoal-600 mb-6",
                                children: "Immersive video backgrounds with call-to-action overlays and cannabis industry branding."
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                lineNumber: 305,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                        lineNumber: 301,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-96 relative",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$hero$2f$HeroVideoWithCTA$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            videoSrc: "/api/placeholder/video",
                            posterSrc: "/api/placeholder/1920/1080",
                            headline: "Premium Cannabis Extracts",
                            subtitle: "Award-winning quality, lab-tested purity",
                            primaryCTA: "Shop Now",
                            onPrimaryCTA: ()=>console.log('Primary CTA clicked'),
                            secondaryCTA: "Learn More",
                            onSecondaryCTA: ()=>console.log('Secondary CTA clicked'),
                            overlayOpacity: 0.5
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 311,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                        lineNumber: 310,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 300,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Animated Card Slider"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 328,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Smooth product carousels with auto-advance, touch support, and responsive design."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 331,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$cards$2f$AnimatedCardSlider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            products: sampleProducts,
                            visibleCards: 3,
                            autoAdvance: 5000,
                            touchEnabled: true,
                            showDots: true,
                            showArrows: true,
                            onCardClick: (product)=>console.log('Card clicked:', product.name)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 335,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 327,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 326,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Tabbed Product Grid"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 350,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Organized product displays with category tabs, pagination, and smooth animations."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 353,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$grids$2f$TabbedProductGrid$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            products: sampleProducts,
                            categories: categories,
                            columns: 3,
                            itemsPerPage: 6,
                            showPagination: true,
                            onProductClick: (product)=>console.log('Product clicked:', product.name)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 357,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 349,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 348,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Masonry Product Showcase"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 371,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Dynamic masonry layouts with filtering capabilities and responsive design."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 374,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$grids$2f$MasonryProductShowcase$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            products: sampleProducts,
                            columns: 3,
                            gap: 20,
                            carouselMode: false,
                            filters: filters,
                            onProductClick: (product)=>console.log('Product clicked:', product.name)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 378,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 370,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 369,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Glitch Text Effects"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 392,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Modern, edgy text effects with customizable intensity and trigger options."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 395,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-6 bg-charcoal-800 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-cream-200 mb-4",
                                            children: "Hover Trigger"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 401,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$effects$2f$GlitchTextEffect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            text: "HOVER ME",
                                            intensity: "medium",
                                            trigger: "hover",
                                            fontSize: "1.5rem",
                                            colors: [
                                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandColors"].apothecary,
                                                '#ff0080',
                                                '#00ff80'
                                            ]
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 402,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 400,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-6 bg-charcoal-800 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-cream-200 mb-4",
                                            children: "Continuous"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 412,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$effects$2f$GlitchTextEffect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            text: "ALWAYS ON",
                                            intensity: "low",
                                            trigger: "continuous",
                                            fontSize: "1.5rem",
                                            colors: [
                                                '#ffd700',
                                                '#ff4500',
                                                '#00ffff'
                                            ]
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 413,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 411,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-6 bg-charcoal-800 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-cream-200 mb-4",
                                            children: "High Intensity"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 423,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$effects$2f$GlitchTextEffect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            text: "INTENSE",
                                            intensity: "high",
                                            trigger: "hover",
                                            fontSize: "1.5rem",
                                            colors: [
                                                '#ff0000',
                                                '#00ff00',
                                                '#0000ff'
                                            ]
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 424,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 422,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 399,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 391,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 390,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Filterable Strain Cards"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 439,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Advanced strain filtering with search, category filters, and sorting options."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 442,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$cards$2f$FilterableStrainCards$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            strains: sampleStrains,
                            enableSearch: true,
                            filters: [
                                {
                                    id: 'type',
                                    name: 'Type',
                                    options: [
                                        'sativa',
                                        'indica',
                                        'hybrid'
                                    ]
                                },
                                {
                                    id: 'thc',
                                    name: 'THC Level',
                                    options: [
                                        'low',
                                        'medium',
                                        'high'
                                    ]
                                }
                            ],
                            sortOptions: [
                                {
                                    id: 'name',
                                    name: 'Name',
                                    field: 'name'
                                },
                                {
                                    id: 'thc',
                                    name: 'THC %',
                                    field: 'thc'
                                }
                            ],
                            cardsPerRow: 3,
                            onStrainClick: (strain)=>console.log('Strain clicked:', strain.name)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 446,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 438,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 437,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Vertical Timeline"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 466,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Showcase cultivation process, company history, or product journey with animated timeline."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 469,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$layout$2f$VerticalTimeline$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            items: timelineItems,
                            theme: "cultivation",
                            alternating: true,
                            showLine: true,
                            animateOnScroll: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 473,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 465,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 464,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Feature Icons with Motion"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 486,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Highlight key features with animated icons and smooth scroll triggers."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 489,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$effects$2f$FeatureIconsWithMotion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            features: features,
                            layout: "grid",
                            animationTrigger: "scroll",
                            columns: 3,
                            iconSize: "lg"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 493,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 485,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 484,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "FAQ Accordion"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 506,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Interactive FAQ section with search, categories, and smooth animations."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 509,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$navigation$2f$FAQAccordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            items: faqItems,
                            allowMultiple: false,
                            showCategories: true,
                            enableSearch: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 513,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 505,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 504,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-4",
                            children: "Announcement Banner"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 525,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-charcoal-600 mb-8",
                            children: "Eye-catching banners for deals, announcements, and important notices."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 528,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$navigation$2f$AnnouncementBanner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    message: "🌿 New Premium Rosin Collection Available - 20% Off This Week!",
                                    type: "deal",
                                    dismissible: true,
                                    actionText: "Shop Now",
                                    onAction: ()=>console.log('Shop now clicked')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 533,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$navigation$2f$AnnouncementBanner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    message: "Lab results now available for all products. View certificates online.",
                                    type: "info",
                                    dismissible: true,
                                    actionText: "View Results",
                                    onAction: ()=>console.log('View results clicked')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 541,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 532,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 524,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 523,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mb-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$layout$2f$SplitMediaLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    media: {
                        type: 'image',
                        src: '/api/placeholder/600/400',
                        alt: 'Cannabis extraction process'
                    },
                    content: {
                        title: 'Our Extraction Process',
                        description: 'We use state-of-the-art CO2 extraction methods to preserve the full spectrum of cannabinoids and terpenes, ensuring the highest quality concentrates.',
                        features: [
                            'CO2 Extraction',
                            'Full Spectrum',
                            'Lab Tested',
                            'Solvent-Free'
                        ],
                        cta: {
                            text: 'Learn More',
                            action: ()=>console.log('Learn more clicked')
                        }
                    },
                    mediaPosition: "left",
                    splitRatio: "50-50",
                    verticalAlign: "center"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 554,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 553,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "bg-cream-100 py-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-3xl font-serif font-bold text-charcoal-800 mb-8 text-center",
                            children: "Component Features"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 578,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    whileHover: {
                                        y: -5
                                    },
                                    className: "bg-white p-6 rounded-lg shadow-soft",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                className: "w-6 h-6 text-primary-600",
                                                fill: "currentColor",
                                                viewBox: "0 0 20 20",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    d: "M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                                    lineNumber: 589,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                                lineNumber: 588,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 587,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-lg font-semibold text-charcoal-800 mb-2",
                                            children: "Cannabis Industry Focused"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 592,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-charcoal-600",
                                            children: "Built specifically for cannabis businesses with industry-appropriate styling and terminology."
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 595,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 583,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    whileHover: {
                                        y: -5
                                    },
                                    className: "bg-white p-6 rounded-lg shadow-soft",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                className: "w-6 h-6 text-sage-600",
                                                fill: "currentColor",
                                                viewBox: "0 0 20 20",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    d: "M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                                    lineNumber: 606,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                                lineNumber: 605,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 604,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-lg font-semibold text-charcoal-800 mb-2",
                                            children: "Responsive Design"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 609,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-charcoal-600",
                                            children: "Mobile-first approach with seamless adaptation across all device sizes and orientations."
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 612,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 600,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    whileHover: {
                                        y: -5
                                    },
                                    className: "bg-white p-6 rounded-lg shadow-soft",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                className: "w-6 h-6 text-gold-600",
                                                fill: "currentColor",
                                                viewBox: "0 0 20 20",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    d: "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                                    lineNumber: 623,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                                lineNumber: 622,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 621,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-lg font-semibold text-charcoal-800 mb-2",
                                            children: "Premium Animations"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 626,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-charcoal-600",
                                            children: "Smooth Framer Motion animations with performance optimization and accessibility support."
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                            lineNumber: 629,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                                    lineNumber: 617,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                            lineNumber: 582,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 577,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 576,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                className: "bg-charcoal-800 text-cream-50 py-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-6 text-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-cream-200",
                        children: "Built with ❤️ for the cannabis industry using React, Framer Motion, and TailwindCSS"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                        lineNumber: 640,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                    lineNumber: 639,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
                lineNumber: 638,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/demos/UIComponentsShowcase.tsx",
        lineNumber: 280,
        columnNumber: 5
    }, this);
};
_c = UIComponentsShowcase;
const __TURBOPACK__default__export__ = UIComponentsShowcase;
var _c;
__turbopack_context__.k.register(_c, "UIComponentsShowcase");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_89c46e33._.js.map