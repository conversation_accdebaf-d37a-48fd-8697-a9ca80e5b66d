'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';
import { VerticalTimelineProps, TimelineItem } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * VerticalTimeline Component
 * 
 * Create a vertical timeline for showing cultivation process, company history, or product journey.
 * Features alternating layout, scroll animations, and themed styling.
 * 
 * @example
 * ```tsx
 * <VerticalTimeline
 *   items={timelineItems}
 *   theme="cultivation"
 *   alternating={true}
 *   showLine={true}
 *   animateOnScroll={true}
 * />
 * ```
 */
const VerticalTimeline: React.FC<VerticalTimelineProps> = ({
  items,
  theme = 'cultivation',
  alternating = true,
  showLine = true,
  animateOnScroll = true,
  className = '',
  style,
}) => {
  // Get theme colors with enhanced plant-inspired palette
  const getThemeColors = () => {
    switch (theme) {
      case 'cultivation':
        return {
          primary: brandColors.primary[600],
          secondary: brandColors.sage[400],
          accent: brandColors.gold[400],
          vineColor: '#2d5a3d', // Deep forest green for vine
          leafColor: '#4a7c59', // Medium green for leaves
          budColor: brandColors.gold[500], // Golden for buds
          rootColor: '#8b4513', // Brown for roots
        };
      case 'company':
        return {
          primary: brandColors.charcoal[700],
          secondary: brandColors.cream[300],
          accent: brandColors.apothecary,
          vineColor: brandColors.charcoal[600],
          leafColor: brandColors.sage[500],
          budColor: brandColors.gold[400],
          rootColor: brandColors.charcoal[500],
        };
      case 'product':
        return {
          primary: brandColors.gold[600],
          secondary: brandColors.primary[300],
          accent: brandColors.sage[500],
          vineColor: brandColors.primary[500],
          leafColor: brandColors.sage[400],
          budColor: brandColors.gold[500],
          rootColor: brandColors.charcoal[400],
        };
      default:
        return {
          primary: brandColors.primary[600],
          secondary: brandColors.sage[400],
          accent: brandColors.gold[400],
          vineColor: '#2d5a3d',
          leafColor: '#4a7c59',
          budColor: brandColors.gold[500],
          rootColor: '#8b4513',
        };
    }
  };

  const themeColors = getThemeColors();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  return (
    <div className={`vertical-timeline relative ${className}`} style={style}>
      {/* Plant Vine Timeline */}
      {showLine && (
        <div className="absolute left-1/2 transform -translate-x-1/2 w-6 h-full">
          {/* Main Vine Stem */}
          <svg
            className="absolute inset-0 w-full h-full"
            viewBox="0 0 24 100"
            preserveAspectRatio="none"
            style={{ height: '100%' }}
          >
            {/* Vine path with organic curves */}
            <path
              d="M12 0 Q10 10 12 20 Q14 30 12 40 Q10 50 12 60 Q14 70 12 80 Q10 90 12 100"
              stroke={themeColors.vineColor}
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
            />

            {/* Decorative leaves along the vine */}
            {items.map((_, index) => {
              const yPosition = (index + 1) * (100 / (items.length + 1));
              const isLeft = index % 2 === 0;
              return (
                <g key={index}>
                  {/* Leaf */}
                  <motion.path
                    d={`M12 ${yPosition} Q${isLeft ? 8 : 16} ${yPosition - 3} ${isLeft ? 6 : 18} ${yPosition} Q${isLeft ? 8 : 16} ${yPosition + 3} 12 ${yPosition}`}
                    fill={themeColors.leafColor}
                    opacity={0.7}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 0.7 }}
                    transition={{
                      delay: index * 0.2,
                      duration: 0.5,
                      ease: brandAnimations.easing.smooth
                    }}
                  />
                  {/* Small branch connecting leaf to vine */}
                  <motion.line
                    x1="12"
                    y1={yPosition}
                    x2={isLeft ? 8 : 16}
                    y2={yPosition}
                    stroke={themeColors.vineColor}
                    strokeWidth="1.5"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{
                      delay: index * 0.2 + 0.3,
                      duration: 0.3
                    }}
                  />
                </g>
              );
            })}
          </svg>

          {/* Animated growing effect */}
          <motion.div
            className="absolute inset-0 w-full h-full overflow-hidden"
            initial={{ clipPath: 'inset(100% 0 0 0)' }}
            animate={{ clipPath: 'inset(0% 0 0 0)' }}
            transition={{
              duration: 2,
              ease: brandAnimations.easing.smooth,
              delay: 0.5
            }}
          >
            <svg
              className="w-full h-full"
              viewBox="0 0 24 100"
              preserveAspectRatio="none"
            >
              <path
                d="M12 0 Q10 10 12 20 Q14 30 12 40 Q10 50 12 60 Q14 70 12 80 Q10 90 12 100"
                stroke={themeColors.primary}
                strokeWidth="2"
                fill="none"
                strokeLinecap="round"
                opacity="0.6"
              />
            </svg>
          </motion.div>
        </div>
      )}

      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: '-100px' }}
        className="space-y-12"
      >
        {items.map((item, index) => (
          <TimelineItemComponent
            key={item.id}
            item={item}
            index={index}
            isAlternating={alternating}
            themeColors={themeColors}
            animateOnScroll={animateOnScroll}
            totalItems={items.length}
            theme={theme}
          />
        ))}
      </motion.div>
    </div>
  );
};

// Plant-themed icon components
const PlantIcons = {
  seed: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <circle cx="12" cy="12" r="3" />
      <path d="M12 9c-1.5 0-3 1-3 3s1.5 3 3 3 3-1 3-3-1.5-3-3-3z" opacity="0.6" />
    </svg>
  ),
  sprout: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M12 22c-1 0-2-1-2-2v-8c0-1 1-2 2-2s2 1 2 2v8c0 1-1 2-2 2z" />
      <path d="M12 12c-2-2-4-3-6-2s-3 3-2 5 3 3 5 2 4-3 6-5z" opacity="0.8" />
      <path d="M12 12c2-2 4-3 6-2s3 3 2 5-3 3-5 2-4-3-6-5z" opacity="0.6" />
    </svg>
  ),
  leaf: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M12 2c5 0 9 4 9 9 0 3-1 5-3 7-1 1-2 2-3 2s-2-1-3-2c-2-2-3-4-3-7 0-5 1.5-9 3-9z" />
      <path d="M12 2c-1.5 0-3 4-3 9 0 3 1 5 3 7" stroke="currentColor" strokeWidth="1" fill="none" opacity="0.4" />
    </svg>
  ),
  flower: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <circle cx="12" cy="12" r="2" />
      <path d="M12 8c-1 0-2 1-2 2s1 2 2 2 2-1 2-2-1-2-2-2z" transform="rotate(0 12 12)" />
      <path d="M12 8c-1 0-2 1-2 2s1 2 2 2 2-1 2-2-1-2-2-2z" transform="rotate(45 12 12)" opacity="0.8" />
      <path d="M12 8c-1 0-2 1-2 2s1 2 2 2 2-1 2-2-1-2-2-2z" transform="rotate(90 12 12)" opacity="0.6" />
      <path d="M12 8c-1 0-2 1-2 2s1 2 2 2 2-1 2-2-1-2-2-2z" transform="rotate(135 12 12)" opacity="0.4" />
    </svg>
  ),
  bud: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <ellipse cx="12" cy="10" rx="4" ry="6" />
      <path d="M8 10c0-2 1-4 2-5s3-1 4 0 2 3 2 5" opacity="0.6" />
      <path d="M12 16v4c0 1-1 2-2 2h4c-1 0-2-1-2-2v-4z" />
    </svg>
  ),
  harvest: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M3 18h18v2H3v-2z" />
      <path d="M12 2l-2 8h4l-2-8z" />
      <path d="M8 10c-1 0-2 1-2 2v4h3v-4c0-1-1-2-2-2z" opacity="0.8" />
      <path d="M16 10c1 0 2 1 2 2v4h-3v-4c0-1 1-2 2-2z" opacity="0.8" />
      <circle cx="12" cy="14" r="2" opacity="0.6" />
    </svg>
  )
};

// Get appropriate plant icon based on index and theme
const getPlantIcon = (index: number, theme: string, totalItems: number) => {
  const iconKeys = Object.keys(PlantIcons) as Array<keyof typeof PlantIcons>;

  if (theme === 'cultivation') {
    // Map cultivation stages to specific icons
    const cultivationStages = ['seed', 'sprout', 'leaf', 'flower', 'bud', 'harvest'];
    const stageIndex = Math.floor((index / totalItems) * cultivationStages.length);
    const stageName = cultivationStages[Math.min(stageIndex, cultivationStages.length - 1)] as keyof typeof PlantIcons;
    return PlantIcons[stageName] || PlantIcons.leaf;
  }

  // For other themes, cycle through icons
  const iconName = iconKeys[index % iconKeys.length];
  return PlantIcons[iconName];
};

// Individual Timeline Item Component
interface TimelineItemComponentProps {
  item: TimelineItem;
  index: number;
  isAlternating: boolean;
  themeColors: {
    primary: string;
    secondary: string;
    accent: string;
    vineColor: string;
    leafColor: string;
    budColor: string;
    rootColor: string;
  };
  animateOnScroll: boolean;
  totalItems: number;
  theme: string;
}

const TimelineItemComponent: React.FC<TimelineItemComponentProps> = ({
  item,
  index,
  isAlternating,
  themeColors,
  animateOnScroll,
  totalItems,
  theme,
}) => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: animateOnScroll,
  });

  const isLeft = isAlternating ? index % 2 === 0 : false;

  const itemVariants = {
    hidden: {
      opacity: 0,
      x: isLeft ? -50 : 50,
      y: 30,
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.6,
        ease: brandAnimations.easing.smooth,
      },
    },
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        duration: 0.5,
        ease: brandAnimations.easing.bounce,
        delay: 0.2,
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      variants={itemVariants}
      initial="hidden"
      animate={animateOnScroll ? (inView ? 'visible' : 'hidden') : 'visible'}
      className={`relative flex items-center ${
        isAlternating
          ? isLeft
            ? 'flex-row-reverse'
            : 'flex-row'
          : 'flex-row'
      }`}
    >
      {/* Content */}
      <div 
        className={`w-full ${
          isAlternating ? 'md:w-5/12' : 'md:w-10/12 md:ml-16'
        } ${isLeft ? 'md:pr-8' : 'md:pl-8'}`}
      >
        <motion.div
          className="bg-gradient-to-br from-cream-50 to-sage-50 rounded-2xl p-6 relative overflow-hidden"
          style={{
            boxShadow: `${brandShadows.soft}, inset 0 1px 0 rgba(255,255,255,0.6)`,
            border: `1px solid ${themeColors.leafColor}20`
          }}
          whileHover={{ y: -4, scale: 1.02 }}
          transition={{ duration: 0.3, ease: brandAnimations.easing.smooth }}
        >
          {/* Organic background pattern */}
          <div
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `radial-gradient(circle at 20% 80%, ${themeColors.leafColor} 2px, transparent 2px),
                               radial-gradient(circle at 80% 20%, ${themeColors.budColor} 1px, transparent 1px),
                               radial-gradient(circle at 40% 40%, ${themeColors.vineColor} 1px, transparent 1px)`,
              backgroundSize: '60px 60px, 40px 40px, 80px 80px'
            }}
          />

          {/* Decorative leaf in corner */}
          <motion.div
            className="absolute top-2 right-2 opacity-20"
            initial={{ rotate: 0, scale: 0 }}
            animate={{ rotate: 15, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill={themeColors.leafColor}>
              <path d="M12 2c5 0 9 4 9 9 0 3-1 5-3 7-1 1-2 2-3 2s-2-1-3-2c-2-2-3-4-3-7 0-5 1.5-9 3-9z" />
            </svg>
          </motion.div>
          {/* Arrow */}
          {isAlternating && (
            <div
              className={`absolute top-6 w-0 h-0 ${
                isLeft
                  ? 'right-0 border-l-8 border-l-cream-50 border-y-8 border-y-transparent'
                  : 'left-0 border-r-8 border-r-cream-50 border-y-8 border-y-transparent'
              }`}
            />
          )}

          {/* Date */}
          <div className="mb-4">
            <motion.span
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-cream-50 relative overflow-hidden"
              style={{
                backgroundColor: themeColors.budColor,
                borderRadius: '20px 8px 20px 8px', // Organic asymmetric shape
                boxShadow: `0 2px 8px ${themeColors.budColor}40`
              }}
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              {/* Subtle shine effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"
                initial={{ x: '-100%' }}
                animate={{ x: '100%' }}
                transition={{ delay: 1, duration: 1.5, ease: 'easeInOut' }}
              />
              <span className="relative z-10">{item.date}</span>
            </motion.span>
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold text-charcoal-800 mb-3">
            {item.title}
          </h3>

          {/* Description */}
          <p className="text-charcoal-600 leading-relaxed mb-4">
            {item.description}
          </p>

          {/* Image */}
          {item.image && (
            <div className="relative h-48 rounded-lg overflow-hidden mb-4">
              <Image
                src={item.image}
                alt={item.title}
                fill
                className="object-cover"
              />
            </div>
          )}
        </motion.div>
      </div>

      {/* Plant-themed Timeline Icon */}
      <motion.div
        variants={iconVariants}
        initial="hidden"
        animate={animateOnScroll ? (inView ? 'visible' : 'hidden') : 'visible'}
        className={`absolute ${
          isAlternating ? 'left-1/2 transform -translate-x-1/2' : 'left-6'
        } z-20`}
      >
        <motion.div
          className="relative"
          whileHover={{ scale: 1.1 }}
          transition={{ duration: 0.2 }}
        >
          {/* Outer glow ring */}
          <motion.div
            className="absolute inset-0 rounded-full"
            style={{
              backgroundColor: themeColors.primary,
              filter: 'blur(8px)',
              opacity: 0.3
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />

          {/* Main icon container */}
          <div
            className="w-14 h-14 rounded-full flex items-center justify-center border-3 border-cream-50 relative overflow-hidden"
            style={{
              backgroundColor: themeColors.primary,
              boxShadow: `0 4px 12px ${themeColors.primary}40, inset 0 1px 0 rgba(255,255,255,0.3)`
            }}
          >
            {/* Organic background pattern inside icon */}
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `radial-gradient(circle at center, ${themeColors.leafColor} 1px, transparent 1px)`,
                backgroundSize: '8px 8px'
              }}
            />

            {/* Icon content */}
            <div className="text-cream-50 relative z-10">
              {item.icon ? item.icon : getPlantIcon(index, theme, totalItems)}
            </div>

            {/* Subtle inner shine */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-white to-transparent opacity-20 rounded-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: [0, 0.3, 0] }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: index * 0.5
              }}
            />
          </div>

          {/* Floating particles around icon */}
          {[...Array(3)].map((_, particleIndex) => (
            <motion.div
              key={particleIndex}
              className="absolute w-1 h-1 rounded-full"
              style={{
                backgroundColor: themeColors.leafColor,
                top: '50%',
                left: '50%'
              }}
              animate={{
                x: [0, Math.cos(particleIndex * 120 * Math.PI / 180) * 25],
                y: [0, Math.sin(particleIndex * 120 * Math.PI / 180) * 25],
                opacity: [0, 0.8, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: index * 0.3 + particleIndex * 0.2,
                ease: 'easeInOut'
              }}
            />
          ))}
        </motion.div>
      </motion.div>

      {/* Spacer for alternating layout */}
      {isAlternating && (
        <div className="w-5/12 hidden md:block" />
      )}
    </motion.div>
  );
};

export default VerticalTimeline;
